/**
 * SnapGrid - Advanced Data Grid Engine
 * A comprehensive client-side data grid
 * Following the single-file architecture pattern of snap-charts.js
 *
 * Features:
 * - Virtual scrolling for performance with large datasets
 * - Column menus with sorting, filtering, grouping
 * - Client-side data operations
 * - Cell editing and custom renderers
 * - Performance monitoring integration
 *
 * @version 1.0.0
 * <AUTHOR> Dashboard Team
 */

class SnapGrid {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        if (!this.container) {
            throw new Error('SnapGrid: Container element not found');
        }

        // Store grid instance on the container element for easy access
        this.container._snapGrid = this;

        // Configuration with defaults
        this.config = {
            columns: options.columns || [],
            data: options.data || [],
            height: options.height || '400px',
            rowHeight: options.rowHeight || 40,
            headerHeight: options.headerHeight || 48,
            virtualScrolling: options.virtualScrolling !== false,
            virtualizeColumns: options.virtualizeColumns || false, // Column virtualization for horizontal performance
            pagination: options.pagination || false,
            pageSize: options.pageSize || 100,
            sortable: options.sortable !== false,
            filterable: options.filterable !== false,
            groupable: options.groupable || false,
            selectable: options.selectable || false,
            editable: options.editable || false,
            resizable: options.resizable !== false,
            theme: options.theme || 'default',
            performance: options.performance !== false,
            rowIdField: options.rowIdField || 'id', // Default field for stable row IDs
            getRowId: options.getRowId || null, // Optional function to get row ID
            allowUnsafeHtml: options.allowUnsafeHtml || false, // Allow unsafe HTML in custom renderers
            sanitizeHTML: options.sanitizeHTML !== false, // Sanitize HTML by default
            smoothScrolling: options.smoothScrolling !== false, // Enable smooth scrolling by default
            ...options
        };

        // Generate unique instance ID
        this.instanceId = 'snap-grid-' + Math.random().toString(36).substr(2, 9);

        // Internal state
        this.state = {
            data: [],
            filteredData: [],
            sortedData: [],
            displayData: [],
            selectedRowKeys: new Set(), // Changed from selectedRows to use stable keys
            rowKeyToIndexMap: new Map(), // Map from row keys to current indices
            sortConfig: [],
            filterConfig: {},
            groupConfig: null,
            expandedGroups: new Set(),
            pageIndex: 0,
            editingCell: null,
            scrollTop: 0,
            scrollLeft: 0,
            visibleRange: { start: 0, end: 0 },
            columnWidths: new Map(),
            columnOrder: [],
            hiddenColumns: new Set(),
            pinnedColumns: { left: [], right: [] },
            columnVisibility: new Map()
        };

        // Performance monitoring
        this.performance = {
            renderTime: 0,
            scrollTime: 0,
            filterTime: 0,
            sortTime: 0,
            memoryUsage: 0
        };

        // Measurement cache for autosize performance
        this.measurementCache = new Map();

        // Event handlers
        this.eventHandlers = new Map();
        this.boundHandlers = new Map();

        // Dialog/menu references for cleanup
        this.activeDialogs = new Set();

        // Active menu tracking for scroll repositioning
        this.activeMenu = null;
        this.activeMenuTarget = null;

        // Resize indicator tracking
        this.resizeIndicator = null;

        // Disable pagination if virtual scrolling is enabled
        if (this.config.virtualScrolling && this.config.pagination) {
            console.warn('SnapGrid: Pagination disabled when virtual scrolling is enabled');
            this.config.pagination = false;
        }

        // Initialize grid
        this.init();
    }

    /**
     * Initialize the grid
     */
    init() {
        try {
            this.validateConfig();
            this.setupContainer();
            this.processColumns();
            this.processData();
            this.createGridStructure();
            this.setupVirtualScrolling();
            this.setupEventListeners();
            this.setupPerformanceMonitoring();
            this.render();
            this.emit('gridReady', { grid: this });
        } catch (error) {
            console.error('SnapGrid initialization failed:', error);
            this.showError('Failed to initialize grid: ' + error.message);
        }
    }

    /**
     * Validate configuration
     */
    validateConfig() {
        if (!Array.isArray(this.config.columns) || this.config.columns.length === 0) {
            throw new Error('Columns configuration is required and must be a non-empty array');
        }

        if (!Array.isArray(this.config.data)) {
            throw new Error('Data must be an array');
        }

        // Validate column definitions
        this.config.columns.forEach((col, index) => {
            if (!col.field && !col.valueGetter) {
                throw new Error(`Column at index ${index} must have either 'field' or 'valueGetter' property`);
            }
        });
    }

    /**
     * Setup container element
     */
    setupContainer() {
        // Preserve existing classes and add grid classes
        this.container.classList.add('snap-grid', this.config.theme);
        this.container.style.height = this.config.height;
        this.container.style.position = 'relative';
        this.container.style.overflow = 'hidden';

        // Add CSS containment for performance
        this.container.style.contain = 'layout style paint';

        // Apply smooth scrolling if enabled
        if (this.config.smoothScrolling && this.elements?.viewport) {
            this.elements.viewport.classList.add('smooth-scroll');
        }
    }

    /**
     * Process column definitions
     */
    processColumns() {
        this.state.columnOrder = this.config.columns.map((col, index) => col.field || `col_${index}`);

        // Set default column widths with minimum width based on header text
        this.config.columns.forEach((col, index) => {
            const field = col.field || `col_${index}`;
            const minWidth = this.calculateMinHeaderWidth(col);
            const configWidth = col.width || 150;
            const width = Math.max(minWidth, configWidth);
            this.state.columnWidths.set(field, width);
        });

        // Set up default pinning for special columns
        this.setupDefaultPinning();
    }

    /**
     * Calculate minimum width needed for header text
     */
    calculateMinHeaderWidth(column) {
        // Special handling for checkbox column - just enough for checkbox + padding
        if (column.field === 'checkbox') {
            return 46; // 16px checkbox + 15px padding each side
        }

        // Special handling for preview column - fixed square size
        if (column.field === 'preview') {
            return 60; // 32px square + 14px padding each side
        }

        // Create a temporary element to measure text width
        const tempElement = document.createElement('div');
        tempElement.style.position = 'absolute';
        tempElement.style.visibility = 'hidden';
        tempElement.style.whiteSpace = 'nowrap';
        tempElement.style.fontFamily = 'Amazon Ember, Arial, sans-serif';
        tempElement.style.fontSize = '14px';
        tempElement.style.fontWeight = '500'; // Medium weight for headers
        tempElement.style.padding = '0 12px'; // Account for cell padding

        // Set the header text
        const headerText = column.headerName || column.field || '';
        tempElement.textContent = headerText;

        // Add to DOM to measure
        document.body.appendChild(tempElement);
        const textWidth = tempElement.offsetWidth;
        document.body.removeChild(tempElement);

        // Add extra space for sort indicators, resize handles, etc.
        const extraSpace = 40; // Space for sort icon + resize handle + margins
        const minWidth = textWidth + extraSpace;

        // Set reasonable bounds
        const absoluteMin = 80; // Never go below this
        const absoluteMax = 300; // Don't auto-expand beyond this

        return Math.max(absoluteMin, Math.min(minWidth, absoluteMax));
    }

    /**
     * Recalculate and enforce minimum widths for all columns
     */
    enforceMinimumWidths() {
        this.config.columns.forEach(column => {
            const field = column.field;
            const currentWidth = this.state.columnWidths.get(field) || 150;
            const minWidth = this.calculateMinHeaderWidth(column);

            if (currentWidth < minWidth) {
                this.setColumnWidth(field, minWidth);
            }
        });
    }

    /**
     * Setup default column pinning
     */
    setupDefaultPinning() {
        // Pin checkbox and preview to left
        const leftPinColumns = ['checkbox', 'preview'];
        leftPinColumns.forEach(field => {
            if (this.config.columns.find(col => col.field === field)) {
                if (!this.state.pinnedColumns.left.includes(field)) {
                    this.state.pinnedColumns.left.push(field);
                }
            }
        });

        // Pin actions to right
        const rightPinColumns = ['actions'];
        rightPinColumns.forEach(field => {
            if (this.config.columns.find(col => col.field === field)) {
                if (!this.state.pinnedColumns.right.includes(field)) {
                    this.state.pinnedColumns.right.push(field);
                }
            }
        });
    }

    /**
     * Process and prepare data
     */
    processData() {
        const startTime = performance.now();

        this.state.data = [...this.config.data];
        this.applyFilters();
        this.applySorting();
        this.applyGrouping();

        this.performance.filterTime = performance.now() - startTime;
    }

    /**
     * Create grid DOM structure
     */
    createGridStructure() {
        this.container.innerHTML = `
            <div class="snap-grid-header" role="rowgroup">
                <div class="snap-grid-header-pinned-left"></div>
                <div class="snap-grid-header-viewport">
                    <div class="snap-grid-header-row" role="row"></div>
                </div>
                <div class="snap-grid-header-pinned-right"></div>
            </div>
            <div class="snap-grid-body" role="rowgroup">
                <div class="snap-grid-pinned-left"></div>
                <div class="snap-grid-viewport">
                    <div class="snap-grid-canvas"></div>
                </div>
                <div class="snap-grid-pinned-right"></div>
            </div>
            <div class="snap-grid-footer" role="contentinfo"></div>
            <div class="snap-grid-overlay" style="display: none;"></div>
        `;

        // Cache DOM elements
        this.elements = {
            header: this.container.querySelector('.snap-grid-header'),
            headerPinnedLeft: this.container.querySelector('.snap-grid-header-pinned-left'),
            headerViewport: this.container.querySelector('.snap-grid-header-viewport'),
            headerRow: this.container.querySelector('.snap-grid-header-row'),
            headerPinnedRight: this.container.querySelector('.snap-grid-header-pinned-right'),
            body: this.container.querySelector('.snap-grid-body'),
            pinnedLeft: this.container.querySelector('.snap-grid-pinned-left'),
            viewport: this.container.querySelector('.snap-grid-viewport'),
            canvas: this.container.querySelector('.snap-grid-canvas'),
            pinnedRight: this.container.querySelector('.snap-grid-pinned-right'),
            footer: this.container.querySelector('.snap-grid-footer'),
            overlay: this.container.querySelector('.snap-grid-overlay')
        };

        // Apply smooth scrolling if enabled
        if (this.config.smoothScrolling) {
            this.elements.viewport.classList.add('smooth-scroll');
        }

        // Set viewport height
        const headerHeight = this.config.headerHeight;
        const footerHeight = this.config.pagination ? 48 : 0;
        this.elements.body.style.height = `calc(100% - ${headerHeight + footerHeight}px)`;
    }

    /**
     * Setup virtual scrolling
     */
    setupVirtualScrolling() {
        if (!this.config.virtualScrolling) return;

        this.virtualScroller = {
            rowHeight: this.config.rowHeight,
            visibleRows: Math.ceil(this.elements.viewport.clientHeight / this.config.rowHeight) + 2,
            totalRows: this.state.displayData.length,
            scrollTop: 0,
            startIndex: 0,
            endIndex: 0
        };

        this.updateVirtualScrolling();
    }

    /**
     * Update virtual scrolling calculations
     */
    updateVirtualScrolling() {
        if (!this.config.virtualScrolling) return;

        const { rowHeight } = this.virtualScroller;
        const scrollTop = this.elements.viewport.scrollTop;
        const visibleHeight = this.elements.viewport.clientHeight;

        this.virtualScroller.startIndex = Math.floor(scrollTop / rowHeight);
        this.virtualScroller.endIndex = Math.min(
            this.virtualScroller.startIndex + Math.ceil(visibleHeight / rowHeight) + 2,
            this.state.displayData.length
        );

        // Update canvas height for proper scrollbar
        const totalHeight = this.state.displayData.length * rowHeight;
        this.elements.canvas.style.height = `${totalHeight}px`;

        this.state.visibleRange = {
            start: this.virtualScroller.startIndex,
            end: this.virtualScroller.endIndex
        };

        // Column virtualization for horizontal performance
        if (this.config.virtualizeColumns) {
            this.updateColumnVirtualization();
        }
    }

    /**
     * Update column virtualization calculations
     */
    updateColumnVirtualization() {
        if (!this.config.virtualizeColumns) return;

        const scrollLeft = this.state.scrollLeft;
        const visibleWidth = this.elements.viewport.clientWidth;

        // Calculate visible column range based on scroll position and column widths
        let cumulativeWidth = 0;
        let startColIndex = 0;
        let endColIndex = this.config.columns.length - 1;

        // Find start column
        for (let i = 0; i < this.config.columns.length; i++) {
            const colWidth = this.getColumnWidth(i);
            if (cumulativeWidth + colWidth > scrollLeft) {
                startColIndex = Math.max(0, i - 1); // Include one column before for smooth scrolling
                break;
            }
            cumulativeWidth += colWidth;
        }

        // Find end column
        cumulativeWidth = 0;
        for (let i = 0; i < this.config.columns.length; i++) {
            const colWidth = this.getColumnWidth(i);
            cumulativeWidth += colWidth;
            if (cumulativeWidth > scrollLeft + visibleWidth) {
                endColIndex = Math.min(this.config.columns.length - 1, i + 1); // Include one column after
                break;
            }
        }

        this.state.visibleColumnRange = {
            start: startColIndex,
            end: endColIndex
        };
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Scroll handling
        this.boundHandlers.set('scroll', this.handleScroll.bind(this));
        this.elements.viewport.addEventListener('scroll', this.boundHandlers.get('scroll'), { passive: true });

        // Prevent independent scrolling of pinned columns - sync with main viewport
        this.boundHandlers.set('pinnedScroll', this.handlePinnedScroll.bind(this));
        if (this.elements.pinnedLeft) {
            this.elements.pinnedLeft.addEventListener('scroll', this.boundHandlers.get('pinnedScroll'), { passive: true });
        }
        if (this.elements.pinnedRight) {
            this.elements.pinnedRight.addEventListener('scroll', this.boundHandlers.get('pinnedScroll'), { passive: true });
        }

        // Resize handling
        this.boundHandlers.set('resize', this.handleResize.bind(this));
        window.addEventListener('resize', this.boundHandlers.get('resize'));

        // Click handling
        this.boundHandlers.set('click', this.handleClick.bind(this));
        this.container.addEventListener('click', this.boundHandlers.get('click'));

        // Keyboard handling
        this.boundHandlers.set('keydown', this.handleKeydown.bind(this));
        this.container.addEventListener('keydown', this.boundHandlers.get('keydown'));


    }



    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        if (!this.config.performance) return;

        // Monitor memory usage
        if (window.performance && window.performance.memory) {
            this.performance.memoryUsage = window.performance.memory.usedJSHeapSize;
        }

        // Setup performance observer for long tasks
        if (window.PerformanceObserver) {
            try {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.duration > 50) {
                            console.warn('SnapGrid: Long task detected:', entry.duration + 'ms');
                        }
                    }
                });
                observer.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                // PerformanceObserver not supported
            }
        }
    }

    /**
     * Render the grid
     */
    render() {
        const startTime = performance.now();

        try {
            this.renderHeader();
            this.renderBody();
            this.renderFooter();

            // Compensate for scrollbar after rendering to ensure proper alignment
            this.compensateHeaderScrollbar();

            // Update filter indicators after rendering
            this.updateFilterIndicators();

            this.performance.renderTime = performance.now() - startTime;
            this.emit('rendered', { renderTime: this.performance.renderTime });
        } catch (error) {
            console.error('SnapGrid render failed:', error);
            this.showError('Render failed: ' + error.message);
        }
    }

    /**
     * Render grid header
     */
    renderHeader() {
        // Clear all header sections
        this.elements.headerPinnedLeft.innerHTML = '';
        this.elements.headerRow.innerHTML = '';
        this.elements.headerPinnedRight.innerHTML = '';

        // Set height for all header sections
        const height = `${this.config.headerHeight}px`;
        this.elements.headerPinnedLeft.style.height = height;
        this.elements.headerViewport.style.height = height;
        this.elements.headerPinnedRight.style.height = height;

        const orderedColumns = this.getOrderedColumns();
        orderedColumns.forEach((column, index) => {
            const headerCell = this.createHeaderCell(column, index);

            // Distribute header cells to appropriate sections
            if (this.state.pinnedColumns.left.includes(column.field)) {
                this.elements.headerPinnedLeft.appendChild(headerCell);
            } else if (this.state.pinnedColumns.right.includes(column.field)) {
                this.elements.headerPinnedRight.appendChild(headerCell);
            } else {
                this.elements.headerRow.appendChild(headerCell);
            }
        });
    }

    /**
     * Get columns in current visual order (respects hidden columns)
     */
    getOrderedColumns() {
        // Start from columnOrder if present, otherwise use config order
        const order = (this.state.columnOrder && this.state.columnOrder.length)
            ? this.state.columnOrder
            : this.config.columns.map(c => c.field);

        // Map to actual column objects and filter out hidden ones
        const ordered = [];
        order.forEach(field => {
            const col = this.config.columns.find(c => c.field === field);
            if (col && !this.state.hiddenColumns.has(col.field)) {
                ordered.push(col);
            }
        });

        // Also include any columns not in order (fallback), excluding hidden
        this.config.columns.forEach(col => {
            if (!order.includes(col.field) && !this.state.hiddenColumns.has(col.field)) {
                ordered.push(col);
            }
        });

        return ordered;
    }

    /**
     * Create header cell
     */
    createHeaderCell(column) {
        const cell = document.createElement('div');
        cell.className = 'snap-grid-header-cell';
        cell.setAttribute('role', 'columnheader');
        cell.setAttribute('data-field', column.field);
        cell.style.width = `${this.state.columnWidths.get(column.field)}px`;

        // Apply pinning styles
        this.applyPinningStyles(cell, column.field);

        // Header content
        const content = document.createElement('div');
        content.className = 'snap-grid-header-content';

        // Special handling for checkbox header (global checkbox)
        if (column.field === 'checkbox') {
            const checkboxWrapper = document.createElement('div');
            checkboxWrapper.className = 'checkbox-wrapper';
            checkboxWrapper.style.display = 'flex';
            checkboxWrapper.style.alignItems = 'center';
            checkboxWrapper.style.justifyContent = 'center';
            checkboxWrapper.style.width = '100%';

            const checkboxImg = document.createElement('img');
            checkboxImg.className = 'checkbox-icon global-checkbox';
            checkboxImg.draggable = false;
            checkboxImg.style.width = '16px';
            checkboxImg.style.height = '16px';
            checkboxImg.style.cursor = 'pointer';

            // Determine checkbox state based on selection
            const totalRows = this.state.displayData.length;
            const selectedRows = this.state.selectedRowKeys.size;

            if (selectedRows === 0) {
                checkboxImg.src = './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = 'Select All';
            } else if (selectedRows === totalRows) {
                checkboxImg.src = './assets/checkbox-ic.svg';
                checkboxImg.alt = 'Deselect All';
            } else {
                checkboxImg.src = './assets/indeterminate-checkbox-ic.svg';
                checkboxImg.alt = 'Select All';
            }

            // Add click handler for select all/none
            checkboxImg.addEventListener('click', (e) => {
                e.stopPropagation();
                if (selectedRows === totalRows) {
                    this.clearSelection();
                } else {
                    this.selectAll();
                }
            });

            checkboxWrapper.appendChild(checkboxImg);
            content.appendChild(checkboxWrapper);
        } else {
            // Column title for regular columns
            const title = document.createElement('span');
            title.className = 'snap-grid-header-title';
            title.textContent = column.headerName || column.field;
            content.appendChild(title);
        }

        // Sort indicator
        if (this.config.sortable && column.sortable !== false) {
            const sortIndicator = this.createSortIndicator(column.field);
            content.appendChild(sortIndicator);
        }

        // Column menu button (hide for Preview, Actions, and Checkbox columns)
        if ((this.config.filterable || this.config.sortable || this.config.groupable) &&
            column.field !== 'preview' && column.field !== 'actions' && column.field !== 'checkbox') {
            const menuButton = this.createColumnMenuButton(column);
            content.appendChild(menuButton);
        }

        cell.appendChild(content);

        // Resize handle (not for preview column - it should be fixed width)
        if (this.config.resizable && column.resizable !== false && column.field !== 'preview') {
            const resizeHandle = this.createResizeHandle(column.field);
            cell.appendChild(resizeHandle);
        }

        return cell;
    }

    /**
     * Create sort indicator
     */
    createSortIndicator(field) {
        const indicator = document.createElement('div');
        indicator.className = 'snap-grid-sort-indicator';

        const sortConfig = this.state.sortConfig.find(s => s.field === field);
        if (sortConfig) {
            indicator.className += ` sorted-${sortConfig.direction}`;
            // No innerHTML needed - CSS background images handle the icons
        }

        return indicator;
    }

    /**
     * Create column menu button
     */
    createColumnMenuButton(column) {
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'snap-grid-column-menu-container';
        buttonContainer.style.position = 'relative';
        buttonContainer.style.display = 'inline-block';

        const button = document.createElement('button');
        button.className = 'snap-grid-column-menu-btn';
        button.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
        button.innerHTML = '⋮';

        button.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showColumnMenu(column, buttonContainer);
        });

        buttonContainer.appendChild(button);

        // Add filter indicator if column has active filter
        if (this.state.filterConfig[column.field]) {
            const indicator = document.createElement('div');
            indicator.className = 'snap-grid-filter-indicator';
            indicator.style.position = 'absolute';
            indicator.style.top = '2px';
            indicator.style.right = '2px';
            indicator.style.width = '6px';
            indicator.style.height = '6px';
            indicator.style.borderRadius = '50%';
            indicator.style.background = 'var(--color-primary-600, #470CED)';
            indicator.style.pointerEvents = 'none';
            indicator.style.zIndex = '1';
            indicator.setAttribute('title', 'Filter applied');

            buttonContainer.appendChild(indicator);
        }

        return buttonContainer;
    }

    /**
     * Create resize handle
     */
    createResizeHandle(field) {
        const handle = document.createElement('div');
        handle.className = 'snap-grid-resize-handle';
        handle.setAttribute('data-field', field);

        let isResizing = false;
        let startX = 0;
        let startWidth = 0;

        handle.addEventListener('mousedown', (e) => {
            isResizing = true;
            startX = e.clientX;
            startWidth = this.state.columnWidths.get(field);

            // Add resizing class to handle
            handle.classList.add('resizing');

            // Create and show resize indicator
            this.showResizeIndicator(e.clientX);

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
            e.preventDefault();
        });

        const handleMouseMove = (e) => {
            if (!isResizing) return;

            const diff = e.clientX - startX;
            const column = this.config.columns.find(col => col.field === field);
            const minWidth = column ? this.calculateMinHeaderWidth(column) : 80;
            const newWidth = Math.max(minWidth, startWidth + diff);
            this.setColumnWidth(field, newWidth);

            // Only update resize indicator position if we're not at minimum width
            // Calculate the actual position where the indicator should stop
            const minDiff = minWidth - startWidth;
            const actualDiff = Math.max(minDiff, diff);
            const indicatorX = startX + actualDiff;

            this.updateResizeIndicator(indicatorX);
        };

        const handleMouseUp = () => {
            isResizing = false;

            // Remove resizing class from handle
            handle.classList.remove('resizing');

            // Hide resize indicator
            this.hideResizeIndicator();

            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        return handle;
    }

    /**
     * Show resize indicator at specified x position
     */
    showResizeIndicator(x) {
        // Remove existing indicator
        this.hideResizeIndicator();

        // Create new indicator
        const indicator = document.createElement('div');
        indicator.className = 'snap-grid-resize-indicator active';

        // Position relative to the grid container
        const containerRect = this.container.getBoundingClientRect();
        const relativeX = x - containerRect.left;

        indicator.style.left = `${relativeX}px`;

        // Add to container
        this.container.appendChild(indicator);
        this.resizeIndicator = indicator;
    }

    /**
     * Update resize indicator position
     */
    updateResizeIndicator(x) {
        if (this.resizeIndicator) {
            const containerRect = this.container.getBoundingClientRect();
            const relativeX = x - containerRect.left;
            this.resizeIndicator.style.left = `${relativeX}px`;
        }
    }

    /**
     * Hide resize indicator
     */
    hideResizeIndicator() {
        if (this.resizeIndicator) {
            this.resizeIndicator.remove();
            this.resizeIndicator = null;
        }
    }

    /**
     * Render grid body
     */
    renderBody() {
        // Clear all containers
        this.elements.canvas.innerHTML = '';
        this.elements.pinnedLeft.innerHTML = '';
        this.elements.pinnedRight.innerHTML = '';

        if (this.config.virtualScrolling) {
            this.renderVirtualRows();
        } else {
            this.renderAllRows();
        }
    }

    /**
     * Render virtual rows
     */
    renderVirtualRows() {
        const { startIndex, endIndex } = this.state.visibleRange;

        for (let i = startIndex; i < endIndex; i++) {
            const rowData = this.state.displayData[i];
            if (rowData) {
                const mainRow = this.createRow(rowData, i);
                // Apply virtual positioning to all row parts
                const leftRows = this.elements.pinnedLeft.querySelectorAll(`[data-row-index="${i}"]`);
                const rightRows = this.elements.pinnedRight.querySelectorAll(`[data-row-index="${i}"]`);

                [mainRow, ...leftRows, ...rightRows].forEach(row => {
                    if (row) {
                        row.style.transform = `translateY(${i * this.config.rowHeight}px)`;
                        row.style.position = 'absolute';
                        row.style.width = '100%';
                    }
                });
            }
        }
    }

    /**
     * Render all rows (non-virtual)
     */
    renderAllRows() {
        this.state.displayData.forEach((rowData, index) => {
            this.createRow(rowData, index);
        });
    }

    /**
     * Create a row element
     */
    createRow(rowData, index) {
        // Handle group headers
        if (rowData.__isGroupHeader) {
            return this.createGroupHeaderRow(rowData, index);
        }

        // Create separate row containers for pinned and scrollable columns
        const mainRow = document.createElement('div');
        mainRow.className = 'snap-grid-row';
        mainRow.setAttribute('role', 'row');
        mainRow.setAttribute('data-row-index', index);
        mainRow.style.height = `${this.config.rowHeight}px`;

        const leftRow = document.createElement('div');
        leftRow.className = 'snap-grid-row snap-grid-pinned-row';
        leftRow.setAttribute('role', 'row');
        leftRow.setAttribute('data-row-index', index);
        leftRow.style.height = `${this.config.rowHeight}px`;

        const rightRow = document.createElement('div');
        rightRow.className = 'snap-grid-row snap-grid-pinned-row';
        rightRow.setAttribute('role', 'row');
        rightRow.setAttribute('data-row-index', index);
        rightRow.style.height = `${this.config.rowHeight}px`;

        // Add row selection class
        const rowKey = this.getRowId(rowData);
        if (this.state.selectedRowKeys.has(rowKey)) {
            mainRow.classList.add('selected');
            leftRow.classList.add('selected');
            rightRow.classList.add('selected');
        }

        // Add alternating row class
        if (index % 2 === 1) {
            mainRow.classList.add('odd');
            leftRow.classList.add('odd');
            rightRow.classList.add('odd');
        }

        // Create cells and distribute them to appropriate containers
        this.getOrderedColumns().forEach((column, colIndex) => {
            const cell = this.createCell(rowData, column, index, colIndex);

            if (this.state.pinnedColumns.left.includes(column.field)) {
                leftRow.appendChild(cell);
            } else if (this.state.pinnedColumns.right.includes(column.field)) {
                rightRow.appendChild(cell);
            } else {
                mainRow.appendChild(cell);
            }
        });

        // Append rows to their respective containers
        this.elements.canvas.appendChild(mainRow);
        if (leftRow.children.length > 0) {
            this.elements.pinnedLeft.appendChild(leftRow);
        }
        if (rightRow.children.length > 0) {
            this.elements.pinnedRight.appendChild(rightRow);
        }

        return mainRow; // Return main row for compatibility
    }

    /**
     * Create a group header row
     */
    createGroupHeaderRow(groupData, index) {
        const row = document.createElement('div');
        row.className = 'snap-grid-row snap-grid-group-row';
        row.setAttribute('role', 'row');
        row.setAttribute('data-row-index', index);
        row.setAttribute('data-group-key', groupData.__groupKey);
        row.style.height = `${this.config.rowHeight}px`;

        const header = document.createElement('div');
        header.className = 'snap-grid-group-header';

        // Toggle button
        const toggle = document.createElement('span');
        toggle.className = `snap-grid-group-toggle ${groupData.__expanded ? 'expanded' : ''}`;
        toggle.innerHTML = '▶';

        // Group title
        const title = document.createElement('span');
        title.className = 'snap-grid-group-title';
        title.textContent = `${groupData.__groupField}: ${groupData.__groupKey}`;

        // Group count
        const count = document.createElement('span');
        count.className = 'snap-grid-group-count';
        count.textContent = `(${groupData.__groupCount} items)`;

        header.appendChild(toggle);
        header.appendChild(title);
        header.appendChild(count);
        row.appendChild(header);

        // Click handler for expand/collapse
        row.addEventListener('click', () => {
            this.toggleGroup(groupData.__groupKey);
        });

        return row;
    }

    /**
     * Toggle group expand/collapse
     */
    toggleGroup(groupKey) {
        if (this.state.expandedGroups.has(groupKey)) {
            this.state.expandedGroups.delete(groupKey);
        } else {
            this.state.expandedGroups.add(groupKey);
        }

        this.processData();
        this.render();
        this.emit('groupToggled', { groupKey, expanded: this.state.expandedGroups.has(groupKey) });
    }

    /**
     * Create a cell element
     */
    createCell(rowData, column, rowIndex, colIndex) {
        const cell = document.createElement('div');
        cell.className = 'snap-grid-cell';
        cell.setAttribute('role', 'gridcell');
        cell.setAttribute('data-field', column.field);
        cell.setAttribute('data-row-index', rowIndex);
        cell.setAttribute('data-col-index', colIndex);
        cell.style.width = `${this.state.columnWidths.get(column.field)}px`;

        // Apply pinning styles
        this.applyPinningStyles(cell, column.field);

        // Get cell value
        const value = this.getCellValue(rowData, column);

        // Render cell content
        const content = this.renderCellContent(value, column, rowData, rowIndex);
        cell.appendChild(content);

        // Add editable class
        if (this.config.editable && column.editable !== false) {
            cell.classList.add('editable');
            cell.setAttribute('tabindex', '0');
        }

        return cell;
    }

    /**
     * Get cell value from row data
     */
    getCellValue(rowData, column) {
        if (column.valueGetter) {
            return column.valueGetter({ data: rowData, column });
        }

        if (column.field) {
            return this.getNestedValue(rowData, column.field);
        }

        return '';
    }

    /**
     * Get nested value from object using dot notation
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : '';
        }, obj);
    }

    /**
     * Render cell content based on column type and renderer
     */
    renderCellContent(value, column, rowData, rowIndex) {
        const container = document.createElement('div');
        container.className = 'snap-grid-cell-content';

        // Use custom renderer if provided
        if (column.cellRenderer) {
            const rendererResult = column.cellRenderer({
                value,
                data: rowData,
                column,
                rowIndex
            });

            if (typeof rendererResult === 'string') {
                // Use centralized cell content helper for HTML sanitization
                this.setCellContent(container, rendererResult, true);
            } else if (rendererResult instanceof HTMLElement) {
                container.appendChild(rendererResult);
            } else {
                container.textContent = String(rendererResult);
            }

            return container;
        }

        // Special handling for checkbox column
        if (column.field === 'checkbox') {
            const checkboxWrapper = document.createElement('div');
            checkboxWrapper.className = 'checkbox-wrapper';
            checkboxWrapper.style.display = 'flex';
            checkboxWrapper.style.alignItems = 'center';
            checkboxWrapper.style.justifyContent = 'center';

            const checkboxImg = document.createElement('img');
            checkboxImg.className = 'checkbox-icon';
            checkboxImg.draggable = false;
            checkboxImg.style.width = '16px';
            checkboxImg.style.height = '16px';
            checkboxImg.style.cursor = 'pointer';

            const isSelected = this.state.selectedRowKeys.has(this.getRowId(rowData));
            checkboxImg.src = isSelected ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
            checkboxImg.alt = isSelected ? 'Checked' : 'Unchecked';

            // Add click handler for row selection
            checkboxImg.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleRowSelection(rowIndex);
            });

            checkboxWrapper.appendChild(checkboxImg);
            container.appendChild(checkboxWrapper);
            return container;
        }

        // Special handling for preview column - product image placeholder
        if (column.field === 'preview') {
            const previewSquare = document.createElement('div');
            previewSquare.className = 'preview-square';
            previewSquare.style.width = '32px';
            previewSquare.style.height = '32px';
            previewSquare.style.borderRadius = '6px';
            previewSquare.style.background = 'var(--bg-secondary)';
            previewSquare.style.cursor = 'pointer';
            previewSquare.style.transition = 'all 0.15s ease';
            previewSquare.style.margin = '0 auto'; // Center in cell

            previewSquare.addEventListener('click', (e) => {
                e.stopPropagation();
                this.emit('previewClicked', { data: rowData, rowIndex });
            });

            previewSquare.addEventListener('mouseenter', () => {
                previewSquare.style.background = 'var(--btn-hover)';
            });

            previewSquare.addEventListener('mouseleave', () => {
                previewSquare.style.background = 'var(--bg-secondary)';
            });

            container.appendChild(previewSquare);
            return container;
        }

        // Special handling for actions column
        if (column.field === 'actions') {
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'actions-container';
            actionsContainer.style.display = 'flex';
            actionsContainer.style.gap = '4px';
            actionsContainer.style.alignItems = 'center';

            // Edit button
            const editBtn = document.createElement('button');
            editBtn.className = 'action-btn edit-btn';
            editBtn.innerHTML = '✏️';
            editBtn.title = 'Edit';
            editBtn.style.padding = '4px';
            editBtn.style.border = 'none';
            editBtn.style.background = 'transparent';
            editBtn.style.cursor = 'pointer';
            editBtn.style.borderRadius = '4px';
            editBtn.style.fontSize = '14px';

            editBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.emit('editClicked', { data: rowData, rowIndex });
            });

            // Delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'action-btn delete-btn';
            deleteBtn.innerHTML = '🗑️';
            deleteBtn.title = 'Delete';
            deleteBtn.style.padding = '4px';
            deleteBtn.style.border = 'none';
            deleteBtn.style.background = 'transparent';
            deleteBtn.style.cursor = 'pointer';
            deleteBtn.style.borderRadius = '4px';
            deleteBtn.style.fontSize = '14px';

            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.emit('deleteClicked', { data: rowData, rowIndex });
            });

            // More actions button
            const moreBtn = document.createElement('button');
            moreBtn.className = 'action-btn more-btn';
            moreBtn.innerHTML = '⋯';
            moreBtn.title = 'More actions';
            moreBtn.style.padding = '4px';
            moreBtn.style.border = 'none';
            moreBtn.style.background = 'transparent';
            moreBtn.style.cursor = 'pointer';
            moreBtn.style.borderRadius = '4px';
            moreBtn.style.fontSize = '14px';

            moreBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.emit('moreActionsClicked', { data: rowData, rowIndex });
            });

            // Add hover effects
            [editBtn, deleteBtn, moreBtn].forEach(btn => {
                btn.addEventListener('mouseenter', () => {
                    btn.style.background = 'var(--color-surface-hover, #f3f4f6)';
                });
                btn.addEventListener('mouseleave', () => {
                    btn.style.background = 'transparent';
                });
            });

            actionsContainer.appendChild(editBtn);
            actionsContainer.appendChild(deleteBtn);
            actionsContainer.appendChild(moreBtn);

            container.appendChild(actionsContainer);
            return container;
        }

        // Special handling for marketplace field with flag icons
        if (column.field === 'marketplace') {
            const flagIcon = document.createElement('img');
            flagIcon.src = `./assets/${value}.svg`;
            flagIcon.alt = value;
            flagIcon.className = 'marketplace-flag';
            flagIcon.style.width = '16px';
            flagIcon.style.height = '16px';
            flagIcon.style.marginRight = '6px';
            flagIcon.style.verticalAlign = 'middle';
            flagIcon.draggable = false;

            const textSpan = document.createElement('span');
            textSpan.textContent = value;
            textSpan.style.verticalAlign = 'middle';

            container.appendChild(flagIcon);
            container.appendChild(textSpan);
            container.style.display = 'flex';
            container.style.alignItems = 'center';

            return container;
        }

        // Built-in renderers based on column type
        switch (column.type) {
            case 'number':
                container.textContent = this.formatNumber(value, column.numberFormat);
                // Removed text-right class - all cells should be left-aligned
                break;

            case 'currency':
                container.textContent = this.formatCurrency(value, column.currencyFormat);
                // Removed text-right class - all cells should be left-aligned
                break;

            case 'date':
                container.textContent = this.formatDate(value, column.dateFormat);
                break;

            case 'boolean':
                container.textContent = this.renderBoolean(value);
                container.classList.add('text-center');
                break;

            case 'link':
                // Links need special handling for safety
                const linkElement = this.renderLink(value, column.linkFormat);
                if (linkElement instanceof HTMLElement) {
                    container.appendChild(linkElement);
                } else {
                    container.textContent = String(value || '');
                }
                break;

            default:
                container.textContent = String(value || '');
        }

        return container;
    }

    /**
     * Centralized cell content assignment with HTML sanitization
     * @param {HTMLElement} el - Target element
     * @param {string} html - HTML content to set
     * @param {boolean} allowHTML - Whether to allow HTML (defaults to false)
     */
    setCellContent(el, html, allowHTML = false) {
        if (allowHTML && this.config.allowUnsafeHtml) {
            el.innerHTML = html;
        } else if (this.config.sanitizeHTML) {
            el.innerHTML = this.sanitizeHTML(html);
        } else {
            el.innerHTML = this.escapeHTML(html);
        }
    }

    /**
     * Format number value
     */
    formatNumber(value, format = {}) {
        if (value === null || value === undefined || value === '') return '';

        const num = Number(value);
        if (isNaN(num)) return String(value);

        return num.toLocaleString(format.locale || 'en-US', {
            minimumFractionDigits: format.minimumFractionDigits || 0,
            maximumFractionDigits: format.maximumFractionDigits || 2
        });
    }

    /**
     * Format currency value
     */
    formatCurrency(value, format = {}) {
        if (value === null || value === undefined || value === '') return '';

        const num = Number(value);
        if (isNaN(num)) return String(value);

        return num.toLocaleString(format.locale || 'en-US', {
            style: 'currency',
            currency: format.currency || 'USD'
        });
    }

    /**
     * Format date value
     */
    formatDate(value, format = {}) {
        if (!value) return '';

        const date = new Date(value);
        if (isNaN(date.getTime())) return String(value);

        if (format.custom) {
            return format.custom(date);
        }

        return date.toLocaleDateString(format.locale || 'en-US', format.options || {});
    }

    /**
     * Render boolean value (safe text-only)
     */
    renderBoolean(value) {
        if (value === null || value === undefined) return '';
        return Boolean(value) ? '✓' : '✗';
    }

    /**
     * Render link value (safe DOM element)
     */
    renderLink(value, format = {}) {
        if (!value) return null;

        const link = document.createElement('a');
        link.className = 'snap-grid-link';
        link.target = format.target || '_blank';
        link.rel = 'noopener noreferrer'; // Security best practice

        // Safely set href and text
        const href = format.href ? format.href(value) : value;
        const text = format.text ? format.text(value) : value;

        link.href = String(href);
        link.textContent = String(text);

        return link;
    }

    /**
     * Apply filters to data
     */
    applyFilters() {
        const startTime = performance.now();

        if (Object.keys(this.state.filterConfig).length === 0) {
            this.state.filteredData = [...this.state.data];
        } else {
            this.state.filteredData = this.state.data.filter(row => {
                return Object.entries(this.state.filterConfig).every(([field, filter]) => {
                    const value = this.getNestedValue(row, field);
                    return this.applyFilter(value, filter);
                });
            });
        }

        this.performance.filterTime = performance.now() - startTime;
    }

    /**
     * Apply single filter to value
     */
    applyFilter(value, filter) {
        const {
            type,
            operator,
            value: filterValue,
            secondOperator,
            secondValue,
            logicOperator,
            checkedValues
        } = filter;

        // First check checkbox filtering (if checkedValues exist)
        if (checkedValues !== undefined && checkedValues !== null) {
            const stringValue = String(value || '');
            if (checkedValues.length === 0) {
                // If no checkboxes are selected, exclude all rows (unselect all functionality)
                return false;
            }
            if (!checkedValues.includes(stringValue)) {
                return false; // Value not in checked list, exclude it
            }
        }

        // Apply first condition
        let firstResult = true;
        if (filterValue || operator === 'blank' || operator === 'notBlank') {
            firstResult = this.applySingleCondition(value, type, operator, filterValue);
        }

        // Apply second condition if exists
        let secondResult = true;
        if (secondOperator && (secondValue || secondOperator === 'blank' || secondOperator === 'notBlank')) {
            secondResult = this.applySingleCondition(value, type, secondOperator, secondValue);

            // Combine results based on logic operator
            if (logicOperator === 'AND') {
                return firstResult && secondResult;
            } else if (logicOperator === 'OR') {
                return firstResult || secondResult;
            }
        }

        return firstResult;
    }

    /**
     * Apply single condition to value
     */
    applySingleCondition(value, type, operator, filterValue) {
        switch (type) {
            case 'text':
                return this.applyTextFilter(value, operator, filterValue);
            case 'number':
                return this.applyNumberFilter(value, operator, filterValue);
            case 'date':
                return this.applyDateFilter(value, operator, filterValue);
            case 'boolean':
                return this.applyBooleanFilter(value, operator, filterValue);
            default:
                return true;
        }
    }

    /**
     * Apply text filter
     */
    applyTextFilter(value, operator, filterValue) {
        const str = String(value || '').toLowerCase();
        const filter = String(filterValue || '').toLowerCase();

        switch (operator) {
            case 'contains': return str.includes(filter);
            case 'notContains': return !str.includes(filter);
            case 'equals': return str === filter;
            case 'notEquals': return str !== filter;
            case 'startsWith': return str.startsWith(filter);
            case 'endsWith': return str.endsWith(filter);
            case 'blank': return !str || str.trim() === '';
            case 'notBlank': return str && str.trim() !== '';
            default: return true;
        }
    }

    /**
     * Apply number filter
     */
    applyNumberFilter(value, operator, filterValue) {
        // Handle blank/not blank first
        if (operator === 'blank') {
            return value === null || value === undefined || value === '';
        }
        if (operator === 'notBlank') {
            return value !== null && value !== undefined && value !== '';
        }

        const num = Number(value);
        const filter = Number(filterValue);

        if (isNaN(num) || isNaN(filter)) return false;

        switch (operator) {
            case 'equals': return num === filter;
            case 'notEquals': return num !== filter;
            case 'lessThan': return num < filter;
            case 'lessThanOrEqual': return num <= filter;
            case 'greaterThan': return num > filter;
            case 'greaterThanOrEqual': return num >= filter;
            case 'inRange':
                // For range filtering, we'd need additional logic to handle two numbers
                // This would require UI changes to accept two number inputs
                return true;
            default: return true;
        }
    }

    /**
     * Apply date filter
     */
    applyDateFilter(value, operator, filterValue) {
        // Handle blank/not blank first
        if (operator === 'blank') {
            return !value || value === '';
        }
        if (operator === 'notBlank') {
            return value && value !== '';
        }

        const date = new Date(value);
        if (isNaN(date.getTime())) return false;

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // Handle preset date ranges
        switch (operator) {
            case 'pleaseSelect':
                return true; // Show all when "Please Select" is chosen
            case 'today':
                const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                return itemDate.getTime() === today.getTime();
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                const itemDateYesterday = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                return itemDateYesterday.getTime() === yesterday.getTime();
            case 'last7Days':
                const sevenDaysAgo = new Date(today);
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                return date >= sevenDaysAgo && date <= now;
            case 'last30Days':
                const thirtyDaysAgo = new Date(today);
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return date >= thirtyDaysAgo && date <= now;
            case 'last90Days':
                const ninetyDaysAgo = new Date(today);
                ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
                return date >= ninetyDaysAgo && date <= now;
            case 'currentMonth':
                return date.getFullYear() === now.getFullYear() && date.getMonth() === now.getMonth();
            case 'lastMonth':
                const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                return date.getFullYear() === lastMonth.getFullYear() && date.getMonth() === lastMonth.getMonth();
            case 'currentYear':
                return date.getFullYear() === now.getFullYear();
            case 'lastYear':
                return date.getFullYear() === now.getFullYear() - 1;
        }

        // Handle custom date comparisons
        const filter = new Date(filterValue);
        if (isNaN(filter.getTime())) return false;

        switch (operator) {
            case 'equals': return date.getTime() === filter.getTime();
            case 'notEquals': return date.getTime() !== filter.getTime();
            case 'lessThan': return date < filter;
            case 'lessThanOrEqual': return date <= filter;
            case 'greaterThan': return date > filter;
            case 'greaterThanOrEqual': return date >= filter;
            case 'inRange':
                // For range filtering, we'd need additional logic to handle two dates
                // This would require UI changes to accept two date inputs
                return true;
            default: return true;
        }
    }

    /**
     * Apply boolean filter
     */
    applyBooleanFilter(value, operator, filterValue) {
        const bool = Boolean(value);
        const filter = Boolean(filterValue);

        switch (operator) {
            case 'equals': return bool === filter;
            case 'notEquals': return bool !== filter;
            default: return true;
        }
    }

    /**
     * Apply sorting to filtered data
     */
    applySorting() {
        const startTime = performance.now();

        if (this.state.sortConfig.length === 0) {
            this.state.sortedData = [...this.state.filteredData];
        } else {
            this.state.sortedData = [...this.state.filteredData].sort((a, b) => {
                for (const sort of this.state.sortConfig) {
                    const aValue = this.getNestedValue(a, sort.field);
                    const bValue = this.getNestedValue(b, sort.field);
                    const result = this.compareValues(aValue, bValue, sort.direction);

                    if (result !== 0) return result;
                }
                return 0;
            });
        }

        this.performance.sortTime = performance.now() - startTime;
    }

    /**
     * Compare two values for sorting
     */
    compareValues(a, b, direction = 'asc') {
        // Handle null/undefined values
        if (a == null && b == null) return 0;
        if (a == null) return direction === 'asc' ? -1 : 1;
        if (b == null) return direction === 'asc' ? 1 : -1;

        // Convert to comparable types
        const aVal = this.getComparableValue(a);
        const bVal = this.getComparableValue(b);

        let result = 0;
        if (aVal < bVal) result = -1;
        else if (aVal > bVal) result = 1;

        return direction === 'desc' ? -result : result;
    }

    /**
     * Get comparable value for sorting
     */
    getComparableValue(value) {
        if (typeof value === 'string') {
            return value.toLowerCase();
        }
        if (value instanceof Date) {
            return value.getTime();
        }
        return value;
    }

    /**
     * Get stable row ID for a row
     */
    getRowId(rowData) {
        if (this.config.getRowId) {
            return this.config.getRowId(rowData);
        }
        return rowData[this.config.rowIdField] || rowData.id || Math.random().toString(36);
    }

    /**
     * Update row key to index mapping
     */
    updateRowKeyMapping() {
        this.state.rowKeyToIndexMap.clear();
        this.state.displayData.forEach((rowData, index) => {
            if (!rowData.__isGroupHeader) {
                const key = this.getRowId(rowData);
                this.state.rowKeyToIndexMap.set(key, index);
            }
        });
    }

    /**
     * Apply grouping to sorted data
     */
    applyGrouping() {
        if (!this.state.groupConfig) {
            this.state.displayData = [...this.state.sortedData];
            this.updateRowKeyMapping();
            return;
        }

        // Group data by specified field
        const groups = new Map();
        this.state.sortedData.forEach(row => {
            const groupValue = this.getNestedValue(row, this.state.groupConfig.field);
            const key = String(groupValue || '');

            if (!groups.has(key)) {
                groups.set(key, []);
            }
            groups.get(key).push(row);
        });

        // Create display data with group headers and expand/collapse
        this.state.displayData = [];
        groups.forEach((rows, groupKey) => {
            // Add group header
            const groupHeader = {
                __isGroupHeader: true,
                __groupKey: groupKey,
                __groupCount: rows.length,
                __groupField: this.state.groupConfig.field,
                __expanded: this.state.expandedGroups.has(groupKey)
            };
            this.state.displayData.push(groupHeader);

            // Add group rows only if expanded
            if (this.state.expandedGroups.has(groupKey)) {
                this.state.displayData.push(...rows);
            }
        });

        // Apply pagination if enabled
        this.applyPagination();

        // Update row key mapping after all processing
        this.updateRowKeyMapping();
    }

    /**
     * Apply pagination to display data
     */
    applyPagination() {
        if (!this.config.pagination) return;

        const pageSize = this.config.pageSize;
        const startIndex = this.state.pageIndex * pageSize;
        const endIndex = startIndex + pageSize;

        this.state.displayData = this.state.displayData.slice(startIndex, endIndex);
    }

    /**
     * Handle scroll events
     */
    handleScroll(event) {
        const startTime = performance.now();

        this.state.scrollTop = this.elements.viewport.scrollTop;
        this.state.scrollLeft = this.elements.viewport.scrollLeft;

        if (this.config.virtualScrolling) {
            this.updateVirtualScrolling();
            this.renderBody();
        }

        // Sync header viewport scroll with body viewport scroll
        if (this.elements.headerViewport) {
            this.elements.headerViewport.scrollLeft = this.state.scrollLeft;
        }

        // Compensate for scrollbar width in header to maintain alignment
        this.compensateHeaderScrollbar();

        // Sync vertical scroll of pinned columns with main viewport
        if (this.elements.pinnedLeft) {
            this.elements.pinnedLeft.scrollTop = this.state.scrollTop;
        }
        if (this.elements.pinnedRight) {
            this.elements.pinnedRight.scrollTop = this.state.scrollTop;
        }

        // Update active menu position if it exists, with collision detection
        if (this.activeMenu && this.activeMenuTarget) {
            try {
                this.positionMenuWithCollisionDetection(this.activeMenu, this.activeMenuTarget);
            } catch (error) {
                console.warn('Error repositioning menu during scroll:', error);
                // Don't close the menu, just skip repositioning this time
            }
        }

        this.performance.scrollTime = performance.now() - startTime;
        this.emit('scroll', {
            scrollTop: this.state.scrollTop,
            scrollLeft: this.state.scrollLeft,
            firstRow: this.state.visibleRange.start,
            lastRow: this.state.visibleRange.end,
            visibleRange: this.state.visibleRange
        });
    }

    /**
     * Compensate for scrollbar width in header to maintain column alignment
     */
    compensateHeaderScrollbar() {
        if (!this.elements.headerViewport || !this.elements.viewport) return;

        // Calculate if there's a vertical scrollbar in the body viewport
        const hasVerticalScrollbar = this.elements.viewport.scrollHeight > this.elements.viewport.clientHeight;

        if (hasVerticalScrollbar) {
            // Get the scrollbar width by comparing offsetWidth and clientWidth
            const scrollbarWidth = this.elements.viewport.offsetWidth - this.elements.viewport.clientWidth;

            // Apply padding-right to header viewport to compensate for scrollbar
            this.elements.headerViewport.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            // Remove padding if no scrollbar
            this.elements.headerViewport.style.paddingRight = '0px';
        }
    }

    /**
     * Handle scroll events from pinned columns - sync with main viewport
     */
    handlePinnedScroll(event) {
        // Sync the main viewport with pinned column scroll
        const scrollTop = event.target.scrollTop;
        if (this.elements.viewport.scrollTop !== scrollTop) {
            this.elements.viewport.scrollTop = scrollTop;
        }
    }

    /**
     * Handle resize events
     */
    handleResize() {
        if (this.config.virtualScrolling) {
            this.setupVirtualScrolling();
            this.render();
        } else {
            // Compensate for scrollbar changes on resize
            this.compensateHeaderScrollbar();
        }
        this.emit('resize');
    }

    /**
     * Handle click events
     */
    handleClick(event) {
        const target = event.target;

        // Header cell click (sorting)
        const headerCell = target.closest('.snap-grid-header-cell');
        if (headerCell && this.config.sortable) {
            const field = headerCell.getAttribute('data-field');
            const column = this.config.columns.find(col => col.field === field);

            if (column && column.sortable !== false) {
                this.toggleSort(field, event.ctrlKey || event.metaKey);
                return;
            }
        }

        // Cell click (editing only)
        const cell = target.closest('.snap-grid-cell');
        if (cell) {
            const rowIndex = parseInt(cell.getAttribute('data-row-index'));
            const colIndex = parseInt(cell.getAttribute('data-col-index'));

            if (this.config.editable && cell.classList.contains('editable')) {
                this.startCellEdit(rowIndex, colIndex);
            }

            return;
        }

        // Row click (row selection)
        const row = target.closest('.snap-grid-row');
        if (row && this.config.selectable) {
            const rowIndex = parseInt(row.getAttribute('data-row-index'));
            this.selectRow(rowIndex, event.ctrlKey || event.metaKey);
        }
    }

    /**
     * Handle keydown events
     */
    handleKeydown(event) {
        const { key, ctrlKey, metaKey, shiftKey } = event;

        // Navigation keys
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
            this.handleArrowKey(key, shiftKey);
            event.preventDefault();
            return;
        }

        // Enter key (edit/confirm)
        if (key === 'Enter') {
            if (this.state.editingCell) {
                this.confirmCellEdit();
            } else if (this.config.editable) {
                this.startCellEditFromSelection();
            }
            event.preventDefault();
            return;
        }

        // Escape key (cancel edit or close open menus/dialogs)
        if (key === 'Escape') {
            if (this.currentFilterDialog) {
                this.hideFilterDialog();
                event.preventDefault();
                return;
            }
            if (this.currentColumnMenu) {
                this.hideColumnMenu();
                event.preventDefault();
                return;
            }
            if (this.state.editingCell) {
                this.cancelCellEdit();
                event.preventDefault();
                return;
            }
            return;
        }

        // Space key (selection)
        if (key === ' ' && this.config.selectable) {
            this.toggleSelectionFromFocus();
            event.preventDefault();
            return;
        }

        // Ctrl/Cmd + A (select all)
        if ((ctrlKey || metaKey) && key === 'a' && this.config.selectable) {
            this.selectAll();
            event.preventDefault();
            return;
        }
    }



    /**
     * Show column menu with tabbed interface
     */
    showColumnMenu(column, button) {
        // Add error checking
        if (!button) {
            console.error('showColumnMenu: button parameter is undefined');
            return;
        }
        if (!column) {
            console.error('showColumnMenu: column parameter is undefined');
            return;
        }

        // Remove existing menu
        this.hideColumnMenu();

        const menu = document.createElement('div');
        menu.className = 'snap-grid-column-menu tabbed-menu';
        menu.setAttribute('role', 'menu');
        menu.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);

        // Create tab header
        const tabHeader = document.createElement('div');
        tabHeader.className = 'menu-tab-header';

        // Tab buttons with icons
        const filterTab = this.createTabButton('filter', 'filters', 'Filter');
        const managementTab = this.createTabButton('management', 'column-man', 'Column Management');
        const visibilityTab = this.createTabButton('visibility', 'show-hide-col', 'Show/Hide Columns');

        tabHeader.appendChild(filterTab);
        tabHeader.appendChild(managementTab);
        tabHeader.appendChild(visibilityTab);

        // Create tab content container
        const tabContent = document.createElement('div');
        tabContent.className = 'menu-tab-content';

        // Create tab panels
        const filterPanel = this.createFilterTab(column);
        const managementPanel = this.createManagementTab(column);
        const visibilityPanel = this.createVisibilityTab();

        tabContent.appendChild(filterPanel);
        tabContent.appendChild(managementPanel);
        tabContent.appendChild(visibilityPanel);

        menu.appendChild(tabHeader);
        menu.appendChild(tabContent);

        // Prevent menu from closing when clicking inside it
        menu.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Set initial active tab
        this.setActiveTab(menu, 'filter');

        // Setup tab switching
        this.setupTabSwitching(menu);

        // Track active menu for scroll repositioning
        const headerCell = button.closest('.snap-grid-header-cell');
        const targetElement = headerCell || button;

        this.activeMenu = menu;
        this.activeMenuTarget = targetElement;

        // Add menu-active class to header cell to show it's the active one
        if (headerCell) {
            headerCell.classList.add('menu-active');
        }

        // Append to grid container for proper relative positioning
        this.container.appendChild(menu);
        this.currentColumnMenu = menu;
        this.activeDialogs.add(menu);

        // Position menu after it's added to DOM for accurate positioning
        try {
            this.positionMenuWithCollisionDetection(menu, targetElement);
        } catch (error) {
            console.error('Error positioning menu:', error);
            // Fallback positioning
            menu.style.position = 'fixed';
            menu.style.top = '100px';
            menu.style.left = '100px';
            menu.style.zIndex = '1001';
        }

        // Ensure menu is properly positioned after being added to DOM
        // This helps with accurate positioning calculations
        requestAnimationFrame(() => {
            if (this.activeMenu === menu && this.activeMenuTarget) {
                this.positionMenuWithCollisionDetection(menu, this.activeMenuTarget);
            }
        });

        // Do not auto-close on outside click; keep menu open during scrolling and in-grid interactions
        // Users can close via ESC, opening another menu, or explicit close controls if added.
    }

    /**
     * Hide column menu
     */
    hideColumnMenu() {
        if (this.currentColumnMenu) {
            this.releaseFocus();
            this.activeDialogs.delete(this.currentColumnMenu);
            this.currentColumnMenu.remove();
            this.currentColumnMenu = null;
        }

        // Remove menu-active class from all header cells
        const activeHeaders = this.container.querySelectorAll('.snap-grid-header-cell.menu-active');
        activeHeaders.forEach(header => header.classList.remove('menu-active'));

        // Clear active menu tracking
        this.activeMenu = null;
        this.activeMenuTarget = null;
    }

    /**
     * Release focus from dialog elements and restore to grid
     */
    releaseFocus() {
        // Remove focus from any focused elements within dialogs
        const activeElement = document.activeElement;
        if (activeElement && (
            activeElement.closest('.snap-grid-column-menu') ||
            activeElement.closest('.snap-grid-filter-dialog') ||
            activeElement.closest('.snap-grid-column-chooser')
        )) {
            activeElement.blur();
        }

        // Restore focus to the grid container if needed
        if (this.container && this.container.tabIndex >= 0) {
            this.container.focus();
        }
    }

    /**
     * Toggle selection of the currently focused row
     */
    toggleSelectionFromFocus() {
        if (!this.config.selectable) return;

        // Find the currently focused row
        const activeElement = document.activeElement;
        const focusedRow = activeElement?.closest('.snap-grid-row[data-row-index]');

        if (focusedRow) {
            const rowIndex = parseInt(focusedRow.getAttribute('data-row-index'));
            if (!isNaN(rowIndex) && rowIndex >= 0 && rowIndex < this.data.length) {
                this.toggleRowSelection(rowIndex);
            }
        }
    }

    /**
     * Create tab button for menu
     */
    createTabButton(tabId, iconName, label) {
        const button = document.createElement('button');
        button.className = 'menu-tab-btn';
        button.setAttribute('data-tab', tabId);
        button.setAttribute('aria-label', label);

        // Create icon element
        const icon = document.createElement('img');
        icon.className = 'tab-icon';
        icon.src = `./assets/${iconName}-inactive-ic.svg`;
        icon.alt = label;
        icon.draggable = false;

        button.appendChild(icon);
        return button;
    }

    /**
     * Create filter tab content
     */
    createFilterTab(column) {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'filter');

        // Check column types for different filter layouts
        const checkboxOnlyColumns = ['marketplace', 'producttype', 'status', 'product_type'];
        const dateOnlyColumns = ['firstsold', 'lastsold', 'firstpublished', 'lastupdated', 'first_sold', 'last_sold', 'first_published', 'last_updated'];
        const numericOnlyColumns = ['price', 'sales', 'returns', 'royalties', 'bsr', 'reviews'];

        const isCheckboxOnly = checkboxOnlyColumns.includes(column.field.toLowerCase());
        const isDateOnly = dateOnlyColumns.includes(column.field.toLowerCase());
        const isNumericOnly = numericOnlyColumns.includes(column.field.toLowerCase());

        // First dropdown (full width) - hidden for checkbox-only columns, shown for date-only columns
        const filterOptions = this.getFilterTypeOptions(column);
        const firstOptionValue = this.getFirstFilterOptionValue(filterOptions);
        const firstDropdown = this.createCustomDropdown(
            filterOptions,
            firstOptionValue
        );
        firstDropdown.classList.add('filter-type-dropdown');
        if (isCheckboxOnly) {
            firstDropdown.style.display = 'none';
        }

        // First filter input (full width with filter icon) - hidden for checkbox-only and date-only columns
        const firstFilterWrapper = document.createElement('div');
        firstFilterWrapper.className = 'filter-input-wrapper';
        if (isCheckboxOnly || isDateOnly) {
            firstFilterWrapper.style.display = 'none';
        }

        const firstFilterIcon = document.createElement('img');
        firstFilterIcon.src = './assets/filters-active-ic.svg';
        firstFilterIcon.className = 'filter-icon';
        firstFilterIcon.alt = 'Filter';

        const firstInput = document.createElement('input');
        firstInput.type = 'text';
        firstInput.className = 'filter-input-with-icon';
        firstInput.placeholder = 'Filter..';

        firstFilterWrapper.appendChild(firstFilterIcon);
        firstFilterWrapper.appendChild(firstInput);

        // AND/OR Logic toggles (initially hidden, always hidden for checkbox-only columns)
        const logicSection = document.createElement('div');
        logicSection.className = 'filter-logic-section';
        logicSection.style.display = 'none';

        const andToggle = this.createLogicToggle('AND', true, logicSection);
        const orToggle = this.createLogicToggle('OR', false, logicSection);
        const separator = document.createElement('span');
        separator.textContent = '/';
        separator.className = 'logic-separator';

        logicSection.appendChild(andToggle);
        logicSection.appendChild(separator);
        logicSection.appendChild(orToggle);

        // Second dropdown (initially hidden, always hidden for checkbox-only columns)
        const secondDropdown = this.createCustomDropdown(
            filterOptions,
            firstOptionValue
        );
        secondDropdown.classList.add('filter-type-dropdown');
        secondDropdown.style.display = 'none';

        // Second filter input (initially hidden, always hidden for checkbox-only columns)
        const secondFilterWrapper = document.createElement('div');
        secondFilterWrapper.className = 'filter-input-wrapper';
        secondFilterWrapper.style.display = 'none';

        const secondFilterIcon = document.createElement('img');
        secondFilterIcon.src = './assets/filters-active-ic.svg';
        secondFilterIcon.className = 'filter-icon';
        secondFilterIcon.alt = 'Filter';

        const secondInput = document.createElement('input');
        secondInput.type = 'text';
        secondInput.className = 'filter-input-with-icon';
        secondInput.placeholder = 'Filter..';

        secondFilterWrapper.appendChild(secondFilterIcon);
        secondFilterWrapper.appendChild(secondInput);

        // Divider
        const divider = document.createElement('div');
        divider.className = 'filter-divider';

        // Search input with icon
        const searchInputWrapper = document.createElement('div');
        searchInputWrapper.className = 'search-input-wrapper';

        const searchIcon = document.createElement('img');
        searchIcon.src = './assets/search-ic.svg';
        searchIcon.className = 'search-icon';
        searchIcon.alt = 'Search';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = 'Search..';

        searchInputWrapper.appendChild(searchIcon);
        searchInputWrapper.appendChild(searchInput);

        // Checkbox list container
        const checkboxList = document.createElement('div');
        checkboxList.className = 'filter-checkbox-list';

        // Get unique values from current column data (filtered by other active filters)
        const uniqueValues = this.getUniqueColumnValues(column.field, true);

        // Set existing filter values if any
        const existingFilter = this.state.filterConfig[column.field];
        if (existingFilter) {
            this.setCustomDropdownValue(firstDropdown, existingFilter.operator);
            firstInput.value = existingFilter.value || '';
        }

        // Helper function to get checked values from filter checkbox list
        const getCheckedValues = () => {
            const checkedValues = [];
            const checkboxes = checkboxList.querySelectorAll('.checkbox-icon:not([data-value="Select All"])');

            checkboxes.forEach(checkbox => {
                if (checkbox.src.includes('checkbox-ic.svg')) {
                    checkedValues.push(checkbox.getAttribute('data-value'));
                }
            });

            return checkedValues;
        };

        // Helper function to regenerate checkbox list based on current filters
        const regenerateCheckboxList = () => {
            // Clear existing checkboxes
            checkboxList.innerHTML = '';

            // Get updated unique values based on current dropdown/text filters
            const updatedValues = this.getUniqueColumnValues(column.field, true);

            // Create "Select All" checkbox
            const selectAllItem = this.createCheckboxItem('Select All', true, true, column, applyFilter);
            checkboxList.appendChild(selectAllItem);

            // Create checkboxes for updated values
            updatedValues.forEach(value => {
                const checkboxItem = this.createCheckboxItem(value, true, false, column, applyFilter);
                checkboxList.appendChild(checkboxItem);
            });
        };

        // Event listeners with stopPropagation to prevent menu closing
        const applyFilter = () => {
            const firstOperator = this.getCustomDropdownValue(firstDropdown);

            if (isDateOnly) {
                // For date-only columns, only use the dropdown selection
                if (firstOperator && firstOperator !== 'pleaseSelect') {
                    this.setFilter(column.field, {
                        type: this.getFilterType(column),
                        operator: firstOperator,
                        value: null // No input value needed for preset date ranges
                    });
                } else {
                    this.clearFilter(column.field);
                }
                return;
            }



            // For other column types, use the existing logic
            const firstValue = firstInput.value;
            const secondOperator = this.getCustomDropdownValue(secondDropdown);
            const secondValue = secondInput.value;

            // Get logic operator (AND/OR)
            const andToggle = logicSection.querySelector('[data-logic="AND"] .logic-checkbox');
            const isAndLogic = andToggle ? andToggle.src.includes('checkbox-ic.svg') : true;
            const logicOperator = isAndLogic ? 'AND' : 'OR';

            // Get checked values from bottom checkbox list (only for non-date columns)
            const checkedValues = isCheckboxOnly ? getCheckedValues() : [];

            // Build compound filter
            const hasFirstFilter = firstValue.trim() || firstOperator === 'blank' || firstOperator === 'notBlank';
            const hasSecondFilter = secondValue.trim() || secondOperator === 'blank' || secondOperator === 'notBlank';

            if (hasFirstFilter || hasSecondFilter || (isCheckboxOnly && checkedValues.length < this.getUniqueColumnValues(column.field).length)) {
                this.setFilter(column.field, {
                    type: this.getFilterType(column),
                    operator: firstOperator,
                    value: firstValue,
                    secondOperator: hasSecondFilter ? secondOperator : null,
                    secondValue: hasSecondFilter ? secondValue : null,
                    logicOperator: hasSecondFilter ? logicOperator : null,
                    checkedValues: isCheckboxOnly ? checkedValues : []
                });
            } else {
                this.clearFilter(column.field);
            }
        };

        // Create "Select All" checkbox
        const selectAllItem = this.createCheckboxItem('Select All', true, true, column, applyFilter);
        checkboxList.appendChild(selectAllItem);

        // Create checkboxes for each unique value
        uniqueValues.forEach(value => {
            const checkboxItem = this.createCheckboxItem(value, true, false, column, applyFilter);
            checkboxList.appendChild(checkboxItem);
        });

        // Search functionality
        searchInput.addEventListener('input', (e) => {
            e.stopPropagation();
            this.filterCheckboxList(checkboxList, e.target.value);
        });

        // Function to toggle second filter visibility (disabled for checkbox-only and date-only columns)
        const toggleSecondFilter = (show) => {
            if (isCheckboxOnly || isDateOnly) return; // Don't show second filter for checkbox-only or date-only columns
            const displayValue = show ? 'block' : 'none';
            logicSection.style.display = show ? 'flex' : 'none';
            secondDropdown.style.display = displayValue;
            secondFilterWrapper.style.display = displayValue;
        };

        // Filter input events
        firstInput.addEventListener('input', (e) => {
            e.stopPropagation();
            const hasValue = e.target.value.trim().length > 0;
            toggleSecondFilter(hasValue);
            // Regenerate checkbox list when text input changes
            regenerateCheckboxList();
            applyFilter();
        });
        firstInput.addEventListener('keydown', (e) => {
            e.stopPropagation();
            if (e.key === 'Enter') {
                applyFilter();
            }
        });
        firstInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Second input events
        secondInput.addEventListener('input', (e) => {
            e.stopPropagation();
            // Regenerate checkbox list when text input changes
            regenerateCheckboxList();
            applyFilter();
        });
        secondInput.addEventListener('keydown', (e) => {
            e.stopPropagation();
            if (e.key === 'Enter') {
                applyFilter();
            }
        });
        secondInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        searchInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Dropdown change events
        firstDropdown.addEventListener('change', (e) => {
            e.stopPropagation();
            // Regenerate checkbox list when dropdown changes
            regenerateCheckboxList();
            applyFilter();
        });

        secondDropdown.addEventListener('change', (e) => {
            e.stopPropagation();
            // Regenerate checkbox list when dropdown changes
            regenerateCheckboxList();
            applyFilter();
        });

        panel.appendChild(firstDropdown);
        panel.appendChild(firstFilterWrapper);
        panel.appendChild(logicSection);
        panel.appendChild(secondDropdown);
        panel.appendChild(secondFilterWrapper);

        // Only add divider, search input, and checkbox list for text and checkbox columns (not date-only or numeric-only)
        if (!isDateOnly && !isNumericOnly) {
            panel.appendChild(divider);
            panel.appendChild(searchInputWrapper);
            panel.appendChild(checkboxList);
        }

        return panel;
    }

    /**
     * Create snap dropdown component
     */
    createCustomDropdown(options, defaultValue = '') {
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown';

        const trigger = document.createElement('div');
        trigger.className = 'dropdown-header';

        const triggerText = document.createElement('span');
        triggerText.textContent = defaultValue;

        const arrow = document.createElement('img');
        arrow.src = './assets/dropdown-ic.svg';
        arrow.alt = 'Dropdown';
        arrow.className = 'dropdown-arrow';

        trigger.appendChild(triggerText);
        trigger.appendChild(arrow);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        // Parse options (can be HTML string or array)
        let optionsList = [];
        if (typeof options === 'string') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = options;
            const optionElements = tempDiv.querySelectorAll('option');
            optionsList = Array.from(optionElements).map(opt => ({
                value: opt.value,
                text: opt.textContent
            }));
        } else if (Array.isArray(options)) {
            optionsList = options;
        }

        optionsList.forEach(option => {
            const optionElement = document.createElement('div');
            optionElement.className = 'dropdown-item';
            optionElement.textContent = option.text || option.value;
            optionElement.setAttribute('data-value', option.value);

            if (option.value === defaultValue) {
                optionElement.classList.add('selected');
                triggerText.textContent = option.text || option.value;
            }

            optionElement.addEventListener('click', (e) => {
                e.stopPropagation();

                // Update selected option
                menu.querySelectorAll('.dropdown-item').forEach(opt => {
                    opt.classList.remove('selected');
                });
                optionElement.classList.add('selected');
                triggerText.textContent = optionElement.textContent;

                // Close dropdown
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');

                // Dispatch change event
                const changeEvent = new CustomEvent('change', {
                    detail: { value: option.value, text: option.text || option.value }
                });
                dropdown.dispatchEvent(changeEvent);
            });

            menu.appendChild(optionElement);
        });

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                d.querySelector('.dropdown-menu').classList.add('hidden');
            });

            if (!isOpen) {
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            dropdown.classList.remove('focused');
            menu.classList.add('hidden');
        });

        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        return dropdown;
    }

    /**
     * Get snap dropdown value
     */
    getCustomDropdownValue(dropdown) {
        const selectedOption = dropdown.querySelector('.dropdown-item.selected');
        return selectedOption ? selectedOption.getAttribute('data-value') : '';
    }

    /**
     * Set snap dropdown value
     */
    setCustomDropdownValue(dropdown, value) {
        const trigger = dropdown.querySelector('.dropdown-header span');
        const options = dropdown.querySelectorAll('.dropdown-item');

        options.forEach(option => {
            option.classList.remove('selected');
            if (option.getAttribute('data-value') === value) {
                option.classList.add('selected');
                trigger.textContent = option.textContent;
            }
        });
    }

    /**
     * Create checkbox item
     */
    createCheckboxItem(value, checked = false, isSelectAll = false, column = null, applyFilterCallback = null) {
        const item = document.createElement('div');
        item.className = 'filter-checkbox-item';
        if (isSelectAll) {
            item.classList.add('select-all');
        }

        // Create checkbox wrapper using the app's checkbox system (same as Show/Hide)
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'checkbox-wrapper';

        const checkboxImg = document.createElement('img');
        checkboxImg.className = 'checkbox-icon';
        checkboxImg.draggable = false;
        checkboxImg.setAttribute('data-value', value);

        // Set appropriate icon based on state
        if (isSelectAll) {
            checkboxImg.src = './assets/indeterminate-checkbox-ic.svg';
            checkboxImg.alt = 'Select All';
        } else {
            checkboxImg.src = checked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
            checkboxImg.alt = checked ? 'Checked' : 'Unchecked';
        }

        const label = document.createElement('label');
        label.className = 'filter-checkbox-label';
        label.textContent = value;

        // Track checked state
        let isChecked = checked;

        // Toggle function
        const toggleCheckbox = () => {
            if (isSelectAll) {
                // For select all, determine current state and toggle accordingly
                const checkboxList = item.parentElement;
                const allIcons = checkboxList.querySelectorAll('.checkbox-icon:not([data-value="Select All"])');
                const checkedCount = Array.from(allIcons).filter(icon => icon.src.includes('checkbox-ic.svg')).length;

                // If all are checked or some are checked, uncheck all
                // If none are checked, check all
                const shouldCheckAll = checkedCount === 0;

                allIcons.forEach(icon => {
                    icon.src = shouldCheckAll ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                    icon.alt = shouldCheckAll ? 'Checked' : 'Unchecked';

                    // CRITICAL FIX: Update the internal state of individual checkboxes
                    const iconItem = icon.closest('.checkbox-item');
                    if (iconItem && iconItem._checkboxState) {
                        iconItem._checkboxState.isChecked = shouldCheckAll;
                    }
                });

                // Update select all icon
                checkboxImg.src = shouldCheckAll ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = shouldCheckAll ? 'Checked' : 'Unchecked';
                isChecked = shouldCheckAll;

                // Apply filter immediately when Select All is toggled
                if (applyFilterCallback) {
                    applyFilterCallback();
                }
            } else {
                // For regular items, get current visual state instead of relying on stale isChecked
                const currentlyChecked = checkboxImg.src.includes('checkbox-ic.svg');
                isChecked = !currentlyChecked; // Toggle based on visual state

                checkboxImg.src = isChecked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = isChecked ? 'Checked' : 'Unchecked';

                // Update select all state
                const checkboxList = item.parentElement;
                const selectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
                const allIcons = checkboxList.querySelectorAll('.checkbox-icon:not([data-value="Select All"])');
                const checkedCount = Array.from(allIcons).filter(icon => icon.src.includes('checkbox-ic.svg')).length;

                if (selectAllIcon) {
                    if (checkedCount === 0) {
                        selectAllIcon.src = './assets/uncheckedbox-ic.svg';
                        selectAllIcon.alt = 'Unchecked';
                    } else if (checkedCount === allIcons.length) {
                        selectAllIcon.src = './assets/checkbox-ic.svg';
                        selectAllIcon.alt = 'Checked';
                    } else {
                        selectAllIcon.src = './assets/indeterminate-checkbox-ic.svg';
                        selectAllIcon.alt = 'Indeterminate';
                    }
                }

                // Apply filter immediately when individual checkbox is toggled
                if (applyFilterCallback) {
                    applyFilterCallback();
                }
            }
        };

        // Store checkbox state reference for Select All updates
        item._checkboxState = { isChecked };

        // Add single click handler to the entire item to prevent double-click issues
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleCheckbox();
        });

        checkboxWrapper.appendChild(checkboxImg);
        item.appendChild(checkboxWrapper);
        item.appendChild(label);

        return item;
    }

    /**
     * Get unique values from column data
     */
    getUniqueColumnValues(field, useFilteredData = false) {
        const values = new Set();
        const dataSource = useFilteredData ? this.getFilteredDataExcludingCheckboxes(field) : this.state.data;

        dataSource.forEach(row => {
            const value = row[field];
            if (value !== null && value !== undefined && value !== '') {
                values.add(String(value));
            }
        });
        return Array.from(values).sort();
    }

    /**
     * Get filtered data excluding checkbox filters for a specific field
     * This is used to populate checkbox lists based on other active filters
     */
    getFilteredDataExcludingCheckboxes(excludeField) {
        if (Object.keys(this.state.filterConfig).length === 0) {
            return [...this.state.data];
        }

        return this.state.data.filter(row => {
            return Object.entries(this.state.filterConfig).every(([field, filter]) => {
                // Skip checkbox filtering for the field we're generating checkboxes for
                if (field === excludeField) {
                    // Apply only dropdown and text input filters, not checkbox filters
                    const {
                        type,
                        operator,
                        value: filterValue,
                        secondOperator,
                        secondValue,
                        logicOperator
                    } = filter;

                    // Apply first condition
                    let firstResult = true;
                    if (filterValue || operator === 'blank' || operator === 'notBlank') {
                        const value = this.getNestedValue(row, field);
                        firstResult = this.applySingleCondition(value, type, operator, filterValue);
                    }

                    // Apply second condition if exists
                    let secondResult = true;
                    if (secondValue || secondOperator === 'blank' || secondOperator === 'notBlank') {
                        const value = this.getNestedValue(row, field);
                        secondResult = this.applySingleCondition(value, type, secondOperator, secondValue);
                    }

                    // Combine results with logic operator
                    if (secondValue || secondOperator === 'blank' || secondOperator === 'notBlank') {
                        return logicOperator === 'AND' ? (firstResult && secondResult) : (firstResult || secondResult);
                    }

                    return firstResult;
                } else {
                    // Apply full filter (including checkboxes) for other fields
                    const value = this.getNestedValue(row, field);
                    return this.applyFilter(value, filter);
                }
            });
        });
    }





    /**
     * Filter checkbox list based on search term
     */
    filterCheckboxList(checkboxList, searchTerm) {
        const items = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)');
        const lowerSearchTerm = searchTerm.toLowerCase();

        items.forEach(item => {
            const label = item.querySelector('.filter-checkbox-label');
            const text = label.textContent.toLowerCase();

            if (text.includes(lowerSearchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    /**
     * Create column management tab content
     */
    createManagementTab(column) {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'management');

        // Sort Ascending option
        const sortAscOption = this.createMenuOptionWithIcon('Sort Ascending', './assets/ascending-ic.svg', () => {
            this.sortColumn(column.field, 'asc');
        });
        panel.appendChild(sortAscOption);

        // Sort Descending option
        const sortDescOption = this.createMenuOptionWithIcon('Sort Descending', './assets/descending-ic.svg', () => {
            this.sortColumn(column.field, 'desc');
        });
        panel.appendChild(sortDescOption);

        // First divider
        const divider1 = document.createElement('div');
        divider1.className = 'column-management-divider';
        panel.appendChild(divider1);

        // Pin Column option with submenu
        const pinContainer = document.createElement('div');
        pinContainer.className = 'pin-column-container';

        const pinOption = document.createElement('div');
        pinOption.className = 'menu-option pin-column-option';

        const pinMain = document.createElement('div');
        pinMain.className = 'pin-column-main';

        const pinIcon = document.createElement('img');
        pinIcon.src = './assets/pin-col-ic.svg';
        pinIcon.alt = 'Pin';
        pinIcon.className = 'menu-option-icon';

        const pinText = document.createElement('span');
        pinText.textContent = 'Pin Column';

        const pinArrow = document.createElement('img');
        pinArrow.src = './assets/arrow-ic.svg';
        pinArrow.alt = 'Arrow';
        pinArrow.className = 'pin-arrow';

        pinMain.appendChild(pinIcon);
        pinMain.appendChild(pinText);
        pinOption.appendChild(pinMain);
        pinOption.appendChild(pinArrow);

        // Pin submenu
        const pinSubmenu = document.createElement('div');
        pinSubmenu.className = 'pin-submenu hidden';

        const currentPinState = this.getColumnPinState(column.field);

        // Pin Left option
        const pinLeftOption = document.createElement('div');
        pinLeftOption.className = 'pin-option';
        const pinLeftCheck = document.createElement('img');
        pinLeftCheck.src = './assets/checked-option-ic.svg';
        pinLeftCheck.className = `check-icon ${currentPinState === 'left' ? '' : 'hidden'}`;
        const pinLeftText = document.createElement('span');
        pinLeftText.textContent = 'Pin Left';
        pinLeftOption.appendChild(pinLeftCheck);
        pinLeftOption.appendChild(pinLeftText);
        pinLeftOption.addEventListener('click', () => {
            this.pinColumn(column.field, 'left');
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        // Pin Right option
        const pinRightOption = document.createElement('div');
        pinRightOption.className = 'pin-option';
        const pinRightCheck = document.createElement('img');
        pinRightCheck.src = './assets/checked-option-ic.svg';
        pinRightCheck.className = `check-icon ${currentPinState === 'right' ? '' : 'hidden'}`;
        const pinRightText = document.createElement('span');
        pinRightText.textContent = 'Pin Right';
        pinRightOption.appendChild(pinRightCheck);
        pinRightOption.appendChild(pinRightText);
        pinRightOption.addEventListener('click', () => {
            this.pinColumn(column.field, 'right');
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        // Don't Pin option
        const dontPinOption = document.createElement('div');
        dontPinOption.className = 'pin-option';
        const dontPinCheck = document.createElement('img');
        dontPinCheck.src = './assets/checked-option-ic.svg';
        dontPinCheck.className = `check-icon ${currentPinState === 'none' ? '' : 'hidden'}`;
        const dontPinText = document.createElement('span');
        dontPinText.textContent = "Don't Pin";
        dontPinOption.appendChild(dontPinCheck);
        dontPinOption.appendChild(dontPinText);
        dontPinOption.addEventListener('click', () => {
            this.unpinColumn(column.field);
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        pinSubmenu.appendChild(pinLeftOption);
        pinSubmenu.appendChild(pinRightOption);
        pinSubmenu.appendChild(dontPinOption);

        // Pin option hover/click behavior - only on pin-column-main
        pinMain.addEventListener('mouseenter', () => {
            pinSubmenu.classList.remove('hidden');
        });
        pinContainer.addEventListener('mouseleave', () => {
            pinSubmenu.classList.add('hidden');
        });

        pinContainer.appendChild(pinOption);
        pinContainer.appendChild(pinSubmenu);
        panel.appendChild(pinContainer);

        // Second divider
        const divider2 = document.createElement('div');
        divider2.className = 'column-management-divider';
        panel.appendChild(divider2);

        // Autosize This Column option
        const autosizeThisOption = this.createMenuOptionWithIcon('Autosize This Column', './assets/autoresize-ic.svg', () => {
            this.autosizeColumn(column.field);
        });
        panel.appendChild(autosizeThisOption);

        // Autosize All Columns option
        const autosizeAllOption = this.createMenuOptionWithIcon('Autosize All Columns', './assets/autoresize-ic.svg', () => {
            this.autosizeAllColumns();
        });
        panel.appendChild(autosizeAllOption);

        // Third divider
        const divider3 = document.createElement('div');
        divider3.className = 'column-management-divider';
        panel.appendChild(divider3);

        // Reset Columns option (no icon)
        const resetOption = document.createElement('div');
        resetOption.className = 'menu-option reset-option';
        const resetText = document.createElement('span');
        resetText.textContent = 'Reset Columns';
        resetOption.appendChild(resetText);
        resetOption.addEventListener('click', () => {
            this.resetColumns();
        });
        panel.appendChild(resetOption);

        return panel;
    }

    /**
     * Create column visibility tab content
     */
    createVisibilityTab() {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'visibility');

        // Search input with icon (matching Filter tab style)
        const searchWrapper = document.createElement('div');
        searchWrapper.className = 'search-input-wrapper';

        const searchIcon = document.createElement('img');
        searchIcon.src = './assets/search-ic.svg';
        searchIcon.className = 'search-icon';
        searchIcon.alt = 'Search';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = 'Search..';

        searchWrapper.appendChild(searchIcon);
        searchWrapper.appendChild(searchInput);

        // Add stopPropagation to search input
        searchInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        searchInput.addEventListener('keydown', (e) => {
            e.stopPropagation();
        });

        // Column list container
        const columnList = document.createElement('div');
        columnList.className = 'column-list';

        // Add drag and drop support to the column list container
        columnList.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            // Update drag indicator position based on mouse position
            const dragIndicator = columnList.querySelector('.drag-indicator');
            if (dragIndicator) {
                const afterElement = this.getDragAfterElement(columnList, e.clientY);
                this.updateDragIndicatorPosition(dragIndicator, columnList, afterElement);
            }
        });

        columnList.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const draggedField = e.dataTransfer.getData('text/plain');

            if (draggedField && this.reorderColumnByIndex) {
                try {
                    // Don't allow dropping fixed columns
                    if (this.isFixedColumn(draggedField)) {
                        return;
                    }

                    // Calculate insertion position based on drop location
                    const afterElement = this.getDragAfterElement(columnList, e.clientY);
                    const validRange = this.getValidInsertionRange();

                    let insertionIndex;
                    if (afterElement == null) {
                        // Insert at the end, but respect valid range
                        insertionIndex = Math.min(validRange.maxIndex, this.state.columnOrder.length);
                    } else {
                        // Insert before the afterElement
                        const afterField = afterElement.getAttribute('data-field');
                        insertionIndex = this.state.columnOrder.indexOf(afterField);

                        // Validate that the insertion position is within valid range
                        if (insertionIndex < validRange.minIndex || insertionIndex > validRange.maxIndex) {
                            // Invalid drop position, don't perform the reorder
                            return;
                        }
                    }

                    this.reorderColumnByIndex(draggedField, insertionIndex);
                } catch (error) {
                    console.error('Error during column reordering:', error);
                    // Don't re-throw the error to prevent it from bubbling up
                }
            }

            // Always remove any existing drag indicator after drop
            const di = columnList.querySelector('.drag-indicator');
            if (di) di.remove();
        });

        // Create column items in current order (exclude checkbox)
        this.getOrderedColumns().forEach((column, index) => {
            if (column.field === 'checkbox') return;
            const item = this.createVisibilityColumnItem(column);
            item.setAttribute('data-column-index', index);
            columnList.appendChild(item);
        });

        // Search functionality
        searchInput.addEventListener('input', (e) => {
            e.stopPropagation();
            const searchTerm = e.target.value.toLowerCase();
            const items = columnList.querySelectorAll('.column-item');

            items.forEach(item => {
                const label = item.querySelector('label').textContent.toLowerCase();
                item.style.display = label.includes(searchTerm) ? 'flex' : 'none';
            });
        });

        panel.appendChild(searchWrapper);
        panel.appendChild(columnList);

        return panel;
    }

    /**
     * Add drag and drop listeners to column item
     */
    addColumnDragListeners(item, column) {
        item.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', column.field);
            e.dataTransfer.effectAllowed = 'move';
            item.classList.add('dragging');

            // Create and add drag indicator if it doesn't exist
            const columnList = item.parentElement;
            let dragIndicator = columnList.querySelector('.drag-indicator');
            if (!dragIndicator) {
                dragIndicator = document.createElement('div');
                dragIndicator.className = 'drag-indicator';
                columnList.appendChild(dragIndicator);
            }

            // Show the indicator immediately
            dragIndicator.classList.add('active');
        });

        item.addEventListener('dragend', () => {
            item.classList.remove('dragging');
            // Remove drag indicator
            const columnList = item.parentElement;
            if (columnList) {
                const dragIndicator = columnList.querySelector('.drag-indicator');
                if (dragIndicator) {
                    dragIndicator.remove();
                }
                // Remove all drag-over classes
                columnList.querySelectorAll('.column-item').forEach(el => {
                    el.classList.remove('drag-over');
                });
            }
        });

        item.addEventListener('dragenter', (e) => {
            e.preventDefault();
            item.classList.add('drag-over');
        });

        item.addEventListener('dragleave', (e) => {
            if (!item.contains(e.relatedTarget)) {
                item.classList.remove('drag-over');
            }
        });


    }

    /**
     * Get the element after which the dragged item should be inserted
     */
    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.column-item:not(.dragging)')];

        // Get valid insertion range
        const validRange = this.getValidInsertionRange();

        // Filter elements to only include those in valid positions
        const validElements = draggableElements.filter((_, index) => {
            // Check if this position would be within valid range
            return index >= validRange.minIndex - 1 && index < validRange.maxIndex;
        });

        return validElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    /**
     * Update drag indicator position based on drop target
     */
    updateDragIndicatorPosition(dragIndicator, columnList, afterElement) {
        if (!dragIndicator) return;

        const columnListRect = columnList.getBoundingClientRect();
        const validRange = this.getValidInsertionRange();

        // Account for scroll position
        const scrollTop = columnList.scrollTop;

        // Get all column items (excluding the one being dragged)
        const allItems = [...columnList.querySelectorAll('.column-item:not(.dragging)')];

        if (afterElement == null) {
            // Position at the end, but respect valid range
            const maxValidIndex = Math.min(validRange.maxIndex - 1, allItems.length - 1);
            const lastValidItem = allItems[maxValidIndex];

            if (lastValidItem) {
                const rect = lastValidItem.getBoundingClientRect();
                dragIndicator.style.top = (rect.bottom - columnListRect.top + scrollTop) + 'px';
            } else {
                // No valid items, position at minimum valid position
                const minValidItem = allItems[validRange.minIndex - 1];
                if (minValidItem) {
                    const rect = minValidItem.getBoundingClientRect();
                    dragIndicator.style.top = (rect.bottom - columnListRect.top + scrollTop) + 'px';
                } else {
                    dragIndicator.style.top = scrollTop + 'px';
                }
            }
        } else {
            // Position before the afterElement, but ensure it's within valid range
            const afterIndex = allItems.indexOf(afterElement);
            if (afterIndex >= validRange.minIndex - 1 && afterIndex < validRange.maxIndex) {
                const rect = afterElement.getBoundingClientRect();
                dragIndicator.style.top = (rect.top - columnListRect.top + scrollTop) + 'px';
            } else {
                // Invalid position, don't show indicator
                dragIndicator.classList.remove('active');
                return;
            }
        }

        dragIndicator.style.left = '0px';
        dragIndicator.style.right = '0px';
        dragIndicator.classList.add('active');
    }

    /**
     * Reorder column by insertion index
     */
    reorderColumnByIndex(draggedField, insertionIndex) {
        if (this.isFixedColumn(draggedField)) return;

        const currentOrder = [...this.state.columnOrder];
        const draggedIndex = currentOrder.indexOf(draggedField);

        if (draggedIndex === -1) return;

        // Remove the dragged item from its current position
        currentOrder.splice(draggedIndex, 1);

        // Adjust insertion index if we removed an item before it
        let adjustedInsertionIndex = insertionIndex;
        if (draggedIndex < insertionIndex) {
            adjustedInsertionIndex--;
        }

        // Recalculate valid range after removing the dragged item
        const updatedValidRange = this.getValidInsertionRangeForOrder(currentOrder);

        // Ensure insertion index is within valid bounds (respecting locked columns)
        adjustedInsertionIndex = Math.max(updatedValidRange.minIndex, Math.min(adjustedInsertionIndex, updatedValidRange.maxIndex));

        // Insert at the new position
        currentOrder.splice(adjustedInsertionIndex, 0, draggedField);

        // Update the state
        this.state.columnOrder = currentOrder;

        // Re-render the grid to reflect the new order
        this.render();

        // Update any open column menus' visibility lists to reflect new order
        this.updateAllOpenVisibilityLists();

        // Emit event
        this.emit('columnReordered', { draggedField, insertionIndex: adjustedInsertionIndex, newOrder: currentOrder });
    }

    /**
     * Reorder column in the grid (legacy method for backward compatibility)
     */
    reorderColumn(draggedField, targetField) {
        if (this.isFixedColumn(draggedField) || this.isFixedColumn(targetField)) return;

        const currentOrder = [...this.state.columnOrder];
        const draggedIndex = currentOrder.indexOf(draggedField);
        const targetIndex = currentOrder.indexOf(targetField);

        if (draggedIndex === -1 || targetIndex === -1) return;

        // Remove the dragged item
        currentOrder.splice(draggedIndex, 1);

        // Insert at the new position
        const newTargetIndex = draggedIndex < targetIndex ? targetIndex : targetIndex + 1;
        currentOrder.splice(newTargetIndex, 0, draggedField);

        // Update the state
        this.state.columnOrder = currentOrder;

        // Re-render the grid to reflect the new order
        this.render();

        // Update any open column menus' visibility lists to reflect new order
        this.updateAllOpenVisibilityLists();

        // Emit event
        this.emit('columnReordered', { draggedField, targetField, newOrder: currentOrder });
    }

    /**
     * Update the column management list to reflect current order
     */
    updateColumnManagementList(columnList) {
        // Clear existing items
        columnList.innerHTML = '';

        // Rebuild the list in the new order using the same format as createVisibilityTab (exclude checkbox)
        this.state.columnOrder.forEach(field => {
            const column = this.config.columns.find(col => col.field === field);
            if (column && column.field !== 'checkbox') {
                const item = this.createVisibilityColumnItem(column);
                columnList.appendChild(item);
            }
        });
    }

    /**
     * Update all open visibility lists (in any open column menus)
     * Keeps the Show/Hide list in sync with header moves
     */
    updateAllOpenVisibilityLists() {
        if (!this.container) return;
        const menus = this.container.querySelectorAll('.snap-grid-column-menu');
        menus.forEach(menu => {
            const visPanel = menu.querySelector('[data-tab-panel="visibility"].menu-tab-panel');
            if (visPanel) {
                const list = visPanel.querySelector('.column-list');
                if (list) {
                    // Completely rebuild, using the same builder used initially
                    this.updateColumnManagementList(list);
                }
            }
        });
    }

    /**
     * Create a column item for the visibility tab (matching the original format)
     */
    createVisibilityColumnItem(column) {
        const item = document.createElement('div');
        item.className = 'column-item';
        item.setAttribute('data-field', column.field);

        const isFixed = this.isFixedColumn(column.field);
        item.draggable = !isFixed;
        if (isFixed) item.classList.add('fixed');

        // Create checkbox wrapper using the app's checkbox system
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'checkbox-wrapper';

        const checkboxImg = document.createElement('img');
        checkboxImg.className = 'checkbox-icon';
        checkboxImg.draggable = false;
        const isVisible = !this.state.hiddenColumns.has(column.field);
        checkboxImg.src = isVisible ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
        checkboxImg.alt = isVisible ? 'Checked' : 'Unchecked';

        // Add grip handle for reordering
        const gripHandle = document.createElement('img');
        gripHandle.src = './assets/grip-ic.svg';
        gripHandle.className = 'grip-handle';
        gripHandle.alt = isFixed ? 'Reordering disabled' : 'Drag to reorder';
        gripHandle.draggable = false;
        if (isFixed) {
            gripHandle.style.opacity = '0.35';
            gripHandle.style.cursor = 'not-allowed';
        }

        const label = document.createElement('label');
        label.textContent = column.headerName || column.field;

        // Toggle functionality
        const toggleColumn = (e) => {
            e.stopPropagation();
            const currentlyVisible = !this.state.hiddenColumns.has(column.field);
            if (currentlyVisible) {
                this.hideColumn(column.field);
                checkboxImg.src = './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = 'Unchecked';
            } else {
                this.showColumn(column.field);
                checkboxImg.src = './assets/checkbox-ic.svg';
                checkboxImg.alt = 'Checked';
            }
        };

        checkboxWrapper.addEventListener('click', toggleColumn);
        label.addEventListener('click', toggleColumn);

        // Add drag and drop event listeners only if not fixed
        if (!isFixed) {
            this.addColumnDragListeners(item, column);
        }

        checkboxWrapper.appendChild(checkboxImg);
        item.appendChild(checkboxWrapper);
        item.appendChild(gripHandle);
        item.appendChild(label);

        return item;
    }

    /**
     * Determine if a column is fixed (non-draggable/non-reorderable)
     */
    isFixedColumn(field) {
        return field === 'checkbox' || field === 'preview' || field === 'actions';
    }

    /**
     * Get the valid insertion range for draggable columns
     * Returns {minIndex, maxIndex} where columns can be inserted
     */
    getValidInsertionRange() {
        return this.getValidInsertionRangeForOrder(this.state.columnOrder);
    }

    /**
     * Get the valid insertion range for a specific column order
     * Returns {minIndex, maxIndex} where columns can be inserted
     */
    getValidInsertionRangeForOrder(columnOrder) {
        // Find the last left-pinned column (preview)
        let minIndex = 0;
        const lastLeftPinnedIndex = columnOrder.indexOf('preview');
        if (lastLeftPinnedIndex !== -1) {
            minIndex = lastLeftPinnedIndex + 1;
        }

        // Find the first right-pinned column (actions)
        let maxIndex = columnOrder.length;
        const firstRightPinnedIndex = columnOrder.indexOf('actions');
        if (firstRightPinnedIndex !== -1) {
            maxIndex = firstRightPinnedIndex;
        }

        return { minIndex, maxIndex };
    }

    /**
     * Create a single column management item
     */
    createColumnManagementItem(column) {
        const item = document.createElement('div');
        item.className = 'column-item';
        const isFixed = this.isFixedColumn(column.field);
        item.draggable = !isFixed;
        if (isFixed) item.classList.add('fixed');
        item.setAttribute('data-field', column.field);

        const gripHandle = document.createElement('div');
        gripHandle.className = 'grip-handle';
        gripHandle.innerHTML = '⋮⋮';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = !this.state.hiddenColumns.has(column.field);
        checkbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                this.showColumn(column.field);
            } else {
                this.hideColumn(column.field);
            }
        });

        const label = document.createElement('span');
        label.textContent = column.headerName || column.field;

        item.appendChild(gripHandle);
        item.appendChild(checkbox);
        item.appendChild(label);

        // Add drag and drop listeners
        this.addColumnDragListeners(item, column);

        return item;
    }

    /**
     * Setup tab switching functionality
     */
    setupTabSwitching(menu) {
        const tabButtons = menu.querySelectorAll('.menu-tab-btn');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                const tabId = button.getAttribute('data-tab');
                this.setActiveTab(menu, tabId);
            });
        });
    }

    /**
     * Set active tab
     */
    setActiveTab(menu, tabId) {
        // Update tab buttons
        const tabButtons = menu.querySelectorAll('.menu-tab-btn');
        tabButtons.forEach(btn => {
            const isActive = btn.getAttribute('data-tab') === tabId;
            btn.classList.toggle('active', isActive);

            // Update icon based on active state
            const icon = btn.querySelector('.tab-icon');
            if (icon) {
                const iconName = this.getIconNameFromTab(btn.getAttribute('data-tab'));
                const state = isActive ? 'active' : 'inactive';
                icon.src = `./assets/${iconName}-${state}-ic.svg`;
            }
        });

        // Update tab panels
        const tabPanels = menu.querySelectorAll('.menu-tab-panel');
        tabPanels.forEach(panel => {
            panel.classList.toggle('active', panel.getAttribute('data-tab-panel') === tabId);
        });
    }

    /**
     * Get icon name from tab ID
     */
    getIconNameFromTab(tabId) {
        const iconMap = {
            'filter': 'filters',
            'management': 'column-man',
            'visibility': 'show-hide-col'
        };
        return iconMap[tabId] || 'filters';
    }

    /**
     * Create logic toggle for AND/OR filter logic
     */
    createLogicToggle(text, isActive, logicSection) {
        const wrapper = document.createElement('div');
        wrapper.className = 'logic-toggle';
        wrapper.setAttribute('data-logic', text);

        const checkbox = document.createElement('img');
        checkbox.className = 'logic-checkbox';
        checkbox.src = isActive ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
        checkbox.alt = isActive ? 'Checked' : 'Unchecked';
        checkbox.draggable = false;

        const label = document.createElement('span');
        label.textContent = text;
        label.className = 'logic-label';

        wrapper.appendChild(checkbox);
        wrapper.appendChild(label);

        // Toggle functionality - mutually exclusive
        wrapper.addEventListener('click', (e) => {
            e.stopPropagation();

            // Get both toggles
            const andToggle = logicSection.querySelector('[data-logic="AND"]');
            const orToggle = logicSection.querySelector('[data-logic="OR"]');

            // Reset both to unchecked
            const andCheckbox = andToggle.querySelector('.logic-checkbox');
            const orCheckbox = orToggle.querySelector('.logic-checkbox');

            andCheckbox.src = './assets/uncheckedbox-ic.svg';
            andCheckbox.alt = 'Unchecked';
            orCheckbox.src = './assets/uncheckedbox-ic.svg';
            orCheckbox.alt = 'Unchecked';

            // Set clicked one to checked
            checkbox.src = './assets/checkbox-ic.svg';
            checkbox.alt = 'Checked';
        });

        return wrapper;
    }

    /**
     * Create menu option with icon
     */
    createMenuOptionWithIcon(text, iconSrc, action) {
        const option = document.createElement('div');
        option.className = 'menu-option-with-icon';

        const iconWrapper = document.createElement('div');
        iconWrapper.className = 'menu-option-icon-wrapper';

        const icon = document.createElement('img');
        icon.src = iconSrc;
        icon.className = 'menu-option-icon';
        icon.alt = text;
        icon.draggable = false;

        const label = document.createElement('span');
        label.textContent = text;
        label.className = 'menu-option-label';

        iconWrapper.appendChild(icon);
        option.appendChild(iconWrapper);
        option.appendChild(label);

        option.addEventListener('click', (e) => {
            e.stopPropagation();
            action();
        });

        return option;
    }

    /**
     * Show filter dialog
     */
    showFilterDialog(column) {
        // Remove existing dialog
        this.hideFilterDialog();

        const dialog = document.createElement('div');
        dialog.className = 'snap-grid-filter-dialog';
        dialog.setAttribute('role', 'dialog');
        dialog.setAttribute('aria-label', `Filter ${column.headerName || column.field}`);

        // Generate unique IDs for this dialog
        const typeId = this.instanceId + '-filter-type';
        const valueId = this.instanceId + '-filter-value';

        // Dialog content
        dialog.innerHTML = `
            <div class="filter-dialog-header">
                <h3>Filter: ${column.headerName || column.field}</h3>
                <button class="filter-dialog-close" aria-label="Close filter dialog">×</button>
            </div>
            <div class="filter-dialog-body">
                <div class="filter-type-section">
                    <label for="${typeId}">Filter Type:</label>
                    <select id="${typeId}" class="filter-type-select">
                        ${this.getFilterTypeOptions(column)}
                    </select>
                </div>
                <div class="filter-value-section">
                    <label for="${valueId}">Filter Value:</label>
                    <input type="text" id="${valueId}" class="filter-value-input" placeholder="Enter filter value...">
                </div>
                <div class="filter-actions">
                    <button class="btn btn-primary filter-apply">Apply Filter</button>
                    <button class="btn btn-secondary filter-clear">Clear Filter</button>
                    <button class="btn btn-secondary filter-cancel">Cancel</button>
                </div>
            </div>
        `;

        // Position dialog
        dialog.style.position = 'fixed';
        dialog.style.top = '50%';
        dialog.style.left = '50%';
        dialog.style.transform = 'translate(-50%, -50%)';
        dialog.style.zIndex = '1001';

        // Position dialog relative to viewport
        this.positionDialog(dialog);

        document.body.appendChild(dialog);
        this.currentFilterDialog = dialog;
        this.activeDialogs.add(dialog);

        // Set current filter values if exists
        const existingFilter = this.state.filterConfig[column.field];
        if (existingFilter) {
            dialog.querySelector(`#${typeId}`).value = existingFilter.operator;
            dialog.querySelector(`#${valueId}`).value = existingFilter.value || '';
        }

        // Event listeners
        this.setupFilterDialogEvents(dialog, column);

        // Add ARIA attributes
        dialog.setAttribute('aria-modal', 'true');


    }

    /**
     * Get the first filter option value from the options string
     */
    getFirstFilterOptionValue(optionsString) {
        if (!optionsString) return '';

        // Parse the HTML options string to get the first option value
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = optionsString;
        const firstOption = tempDiv.querySelector('option');

        return firstOption ? firstOption.textContent : '';
    }

    /**
     * Get filter type options based on column field and type
     */
    getFilterTypeOptions(column) {
        const field = column.field;

        // Text-based columns (ASIN, Brand, Product Title)
        const textColumns = ['asin', 'brand', 'productTitle', 'product_title'];
        const textOptions = [
            { value: 'contains', label: 'Contains' },
            { value: 'notContains', label: 'Not Contains' },
            { value: 'startsWith', label: 'Starts with' },
            { value: 'endsWith', label: 'Ends with' },
            { value: 'blank', label: 'Blank' },
            { value: 'notBlank', label: 'Not Blank' }
        ];

        // Numeric columns (Price, Sales, Returns, Royalties, BSR, Reviews)
        const numericColumns = ['price', 'sales', 'returns', 'royalties', 'bsr', 'reviews'];
        const numericOptions = [
            { value: 'equals', label: 'Equals' },
            { value: 'notEquals', label: 'Not equal' },
            { value: 'lessThan', label: 'Less than' },
            { value: 'lessThanOrEqual', label: 'Less than or equals' },
            { value: 'greaterThan', label: 'Greater than' },
            { value: 'greaterThanOrEqual', label: 'Greater than or equals' },
            { value: 'inRange', label: 'In range' },
            { value: 'blank', label: 'Blank' },
            { value: 'notBlank', label: 'Not blank' }
        ];

        // Date columns (First Sold, Last Sold, First Published Date, Last Updated Date)
        const dateColumns = ['firstsold', 'lastsold', 'firstpublished', 'lastupdated', 'first_sold', 'last_sold', 'first_published', 'last_updated'];
        const dateOptions = [
            { value: 'pleaseSelect', label: 'Please Select' },
            { value: 'today', label: 'Today' },
            { value: 'yesterday', label: 'Yesterday' },
            { value: 'last7Days', label: 'Last 7 Days' },
            { value: 'last30Days', label: 'Last 30 Days' },
            { value: 'last90Days', label: 'Last 90 Days' },
            { value: 'currentMonth', label: 'Current Month' },
            { value: 'lastMonth', label: 'Last Month' },
            { value: 'currentYear', label: 'Current Year' },
            { value: 'lastYear', label: 'Last Year' },
            { value: 'equals', label: 'Equals' },
            { value: 'notEquals', label: 'Not equal' },
            { value: 'lessThan', label: 'Less than' },
            { value: 'greaterThan', label: 'Greater than' },
            { value: 'inRange', label: 'In range' }
        ];

        // Checkbox-only columns (Marketplace, Product Type, Status) - return empty for dropdown
        const checkboxOnlyColumns = ['marketplace', 'producttype', 'status', 'product_type'];

        // Determine which options to use based on field name
        let options;
        if (textColumns.includes(field.toLowerCase())) {
            options = textOptions;
        } else if (numericColumns.includes(field.toLowerCase())) {
            options = numericOptions;
        } else if (dateColumns.includes(field.toLowerCase())) {
            options = dateOptions;
        } else if (checkboxOnlyColumns.includes(field.toLowerCase())) {
            // For checkbox-only columns, return empty options (no dropdown filters)
            options = [];
        } else {
            // Default fallback to text options
            options = textOptions;
        }

        return options.map(opt =>
            `<option value="${opt.value}">${opt.label}</option>`
        ).join('');
    }

    /**
     * Get filter type based on column type
     */
    getFilterType(column) {
        const type = column.type || 'text';

        switch (type) {
            case 'number':
            case 'currency':
                return 'number';
            case 'date':
                return 'date';
            case 'boolean':
                return 'boolean';
            default:
                return 'text';
        }
    }

    /**
     * Setup filter dialog event listeners
     */
    setupFilterDialogEvents(dialog, column) {
        const typeId = this.instanceId + '-filter-type';
        const valueId = this.instanceId + '-filter-value';
        const typeSelect = dialog.querySelector(`#${typeId}`);
        const valueInput = dialog.querySelector(`#${valueId}`);
        const applyBtn = dialog.querySelector('.filter-apply');
        const clearBtn = dialog.querySelector('.filter-clear');
        const cancelBtn = dialog.querySelector('.filter-cancel');
        const closeBtn = dialog.querySelector('.filter-dialog-close');

        // Type change handler
        typeSelect.addEventListener('change', () => {
            const selectedType = typeSelect.value;
            const isBlankFilter = selectedType === 'blank' || selectedType === 'notBlank';
            valueInput.disabled = isBlankFilter;
            valueInput.placeholder = isBlankFilter ? 'No value needed' : 'Enter filter value...';

            // Set input type based on column type
            if (column.type === 'number' || column.type === 'currency') {
                valueInput.type = 'number';
            } else if (column.type === 'date') {
                valueInput.type = 'date';
            } else {
                valueInput.type = 'text';
            }
        });

        // Trigger initial type change
        typeSelect.dispatchEvent(new Event('change'));

        // Apply filter
        applyBtn.addEventListener('click', () => {
            const filterType = typeSelect.value;
            const filterValue = valueInput.value;

            if ((filterType !== 'blank' && filterType !== 'notBlank') && !filterValue.trim()) {
                alert('Please enter a filter value');
                return;
            }

            this.setFilter(column.field, {
                type: column.type || 'text',
                operator: filterType,
                value: filterValue
            });

            this.hideFilterDialog();
        });

        // Clear filter
        clearBtn.addEventListener('click', () => {
            this.clearFilter(column.field);
            this.hideFilterDialog();
        });

        // Cancel
        cancelBtn.addEventListener('click', () => {
            this.hideFilterDialog();
        });

        // Close button
        closeBtn.addEventListener('click', () => {
            this.hideFilterDialog();
        });

        // Enter key to apply
        valueInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                applyBtn.click();
            } else if (e.key === 'Escape') {
                this.hideFilterDialog();
            }
        });

        // Close on outside click
        setTimeout(() => {
            document.addEventListener('click', (e) => {
                if (!dialog.contains(e.target)) {
                    this.hideFilterDialog();
                }
            }, { once: true });
        }, 100);
    }

    /**
     * Hide filter dialog
     */
    hideFilterDialog() {
        if (this.currentFilterDialog) {
            this.releaseFocus();
            this.activeDialogs.delete(this.currentFilterDialog);
            this.currentFilterDialog.remove();
            this.currentFilterDialog = null;
        }
    }

    /**
     * Create menu option
     */
    createMenuOption(text, onClick) {
        const option = document.createElement('div');
        option.className = 'snap-grid-menu-option';
        option.setAttribute('role', 'menuitem');
        option.textContent = text;
        option.addEventListener('click', (e) => {
            e.stopPropagation();
            onClick(e);
        });
        return option;
    }

    /**
     * Create menu separator
     */
    createMenuSeparator() {
        const separator = document.createElement('div');
        separator.className = 'snap-grid-menu-separator';
        return separator;
    }

    /**
     * Create menu submenu
     */
    createMenuSubmenu(text, items) {
        const submenu = document.createElement('div');
        submenu.className = 'snap-grid-menu-submenu';

        const trigger = document.createElement('div');
        trigger.className = 'snap-grid-menu-option submenu-trigger';
        trigger.innerHTML = `${text} <span class="submenu-arrow">▶</span>`;

        const submenuContent = document.createElement('div');
        submenuContent.className = 'snap-grid-submenu-content';

        items.forEach(item => {
            const option = document.createElement('div');
            option.className = 'snap-grid-menu-option';
            if (item.checked) {
                option.classList.add('checked');
            }
            option.textContent = item.label;
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                item.action(e);
            });
            submenuContent.appendChild(option);
        });

        submenu.appendChild(trigger);
        submenu.appendChild(submenuContent);

        // Show/hide submenu on hover
        let hoverTimeout;
        trigger.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
            submenuContent.style.display = 'block';
        });

        submenu.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                submenuContent.style.display = 'none';
            }, 200);
        });

        return submenu;
    }

    /**
     * Check if column is pinned
     */
    isColumnPinned(field, side = null) {
        if (side) {
            return this.state.pinnedColumns[side].includes(field);
        }
        return this.state.pinnedColumns.left.includes(field) ||
               this.state.pinnedColumns.right.includes(field);
    }

    /**
     * Get column pin state
     */
    getColumnPinState(field) {
        if (this.state.pinnedColumns.left.includes(field)) {
            return 'left';
        } else if (this.state.pinnedColumns.right.includes(field)) {
            return 'right';
        } else {
            return 'none';
        }
    }

    /**
     * Sort column
     */
    sortColumn(field, direction) {
        // Update the sortConfig array instead of the old sortBy/sortDirection
        this.state.sortConfig = [{ field, direction }];
        this.processData(); // Apply the sorting to the data
        this.render();
        this.emit('columnSorted', { field, direction });
    }

    /**
     * Toggle row selection
     */
    toggleRowSelection(rowIndex) {
        const rowData = this.state.displayData[rowIndex];
        if (!rowData) return;

        const rowKey = this.getRowId(rowData);

        if (this.state.selectedRowKeys.has(rowKey)) {
            this.state.selectedRowKeys.delete(rowKey);
        } else {
            this.state.selectedRowKeys.add(rowKey);
        }

        this.render();
        this.emit('selectionChanged', {
            selectedKeys: Array.from(this.state.selectedRowKeys),
            selectedRows: this.getSelectedRows()
        });
    }

    /**
     * Get selected row data
     */
    getSelectedRows() {
        return this.state.displayData.filter((rowData) => {
            const rowKey = this.getRowId(rowData);
            return this.state.selectedRowKeys.has(rowKey);
        });
    }

    /**
     * Pin column to left or right
     */
    pinColumn(field, side) {
        // Remove from other side first
        this.unpinColumn(field);

        if (!this.state.pinnedColumns[side].includes(field)) {
            this.state.pinnedColumns[side].push(field);
            this.render();
            this.emit('columnPinned', { field, side });
        }
    }

    /**
     * Unpin column
     */
    unpinColumn(field) {
        this.state.pinnedColumns.left = this.state.pinnedColumns.left.filter(f => f !== field);
        this.state.pinnedColumns.right = this.state.pinnedColumns.right.filter(f => f !== field);
        this.render();
        this.emit('columnUnpinned', { field });
    }

    /**
     * Update pin submenu check icons based on current pin state
     */
    updatePinSubmenu(field, pinSubmenu) {
        if (!pinSubmenu) return;

        const currentPinState = this.getColumnPinState(field);

        // Find all check icons in the pin submenu
        const pinLeftCheck = pinSubmenu.querySelector('.pin-option:nth-child(1) .check-icon');
        const pinRightCheck = pinSubmenu.querySelector('.pin-option:nth-child(2) .check-icon');
        const dontPinCheck = pinSubmenu.querySelector('.pin-option:nth-child(3) .check-icon');

        // Update visibility based on current pin state
        if (pinLeftCheck) {
            pinLeftCheck.className = `check-icon ${currentPinState === 'left' ? '' : 'hidden'}`;
        }
        if (pinRightCheck) {
            pinRightCheck.className = `check-icon ${currentPinState === 'right' ? '' : 'hidden'}`;
        }
        if (dontPinCheck) {
            dontPinCheck.className = `check-icon ${currentPinState === 'none' ? '' : 'hidden'}`;
        }
    }

    /**
     * Autosize column to fit content
     */
    autosizeColumn(field) {
        const column = this.config.columns.find(col => col.field === field);
        if (!column) return;

        // Skip autosize for fixed-width columns (Checkbox, Preview, Actions)
        const excludedColumns = ['checkbox', 'preview', 'actions'];
        if (excludedColumns.includes(field)) {
            return;
        }

        // Get computed font styles from actual DOM elements
        const fonts = this.getComputedFonts();

        // Measure header width
        const headerText = column.headerName || column.field;
        let maxWidth = this.measureTextWidth(headerText, fonts.header, column.field);

        // Sample visible rows for content measurement (more efficient)
        const visibleData = this.config.virtualScrolling ?
            this.state.displayData.slice(this.state.visibleRange.start, this.state.visibleRange.end) :
            this.state.displayData.slice(0, 100); // Limit to first 100 for performance

        // Measure content widths
        visibleData.forEach(rowData => {
            if (rowData && !rowData.__isGroupHeader) {
                const value = this.getCellValue(rowData, column);
                let displayValue = String(value || '');

                // Apply formatting for accurate measurement
                if (column.type === 'currency') {
                    displayValue = this.formatCurrency(value, column.currencyFormat);
                } else if (column.type === 'number') {
                    displayValue = this.formatNumber(value, column.numberFormat);
                } else if (column.type === 'date') {
                    displayValue = this.formatDate(value, column.dateFormat);
                } else if (column.type === 'boolean') {
                    displayValue = value ? '✓' : '✗';
                }

                const contentWidth = this.measureTextWidth(displayValue, fonts.cell, column.field);
                maxWidth = Math.max(maxWidth, contentWidth);
            }
        });

        // Add padding for cell content (left + right padding + borders)
        const padding = 24; // 12px each side
        const scrollbarBuffer = 20; // Buffer for potential scrollbars

        // Apply constraints: min 60px, max 600px
        const optimalWidth = Math.max(60, Math.min(600, maxWidth + padding + scrollbarBuffer));

        // Update column width and trigger re-render
        this.setColumnWidth(field, optimalWidth);

        // If column is pinned, recalculate pin offsets
        if (this.isColumnPinned(field)) {
            this.render();
        }
    }

    /**
     * Autosize all columns
     */
    autosizeAllColumns() {
        // Columns that should not be autosized
        const excludedColumns = ['checkbox', 'preview', 'actions'];

        this.config.columns.forEach(column => {
            if (!this.state.hiddenColumns.has(column.field) && !excludedColumns.includes(column.field)) {
                this.autosizeColumn(column.field);
            }
        });
    }

    /**
     * Get computed font styles from DOM elements
     */
    getComputedFonts() {
        // Cache fonts to avoid recomputation
        if (this._cachedFonts) {
            return this._cachedFonts;
        }

        // Create temporary elements to get computed styles
        const headerElement = document.createElement('div');
        headerElement.className = 'snap-grid-header-cell';
        headerElement.style.visibility = 'hidden';
        headerElement.style.position = 'absolute';
        headerElement.style.top = '-9999px';

        const cellElement = document.createElement('div');
        cellElement.className = 'snap-grid-cell';
        cellElement.style.visibility = 'hidden';
        cellElement.style.position = 'absolute';
        cellElement.style.top = '-9999px';

        // Append to container to get computed styles
        this.container.appendChild(headerElement);
        this.container.appendChild(cellElement);

        // Get computed styles
        const headerStyle = window.getComputedStyle(headerElement);
        const cellStyle = window.getComputedStyle(cellElement);

        this._cachedFonts = {
            header: `${headerStyle.fontWeight} ${headerStyle.fontSize} ${headerStyle.fontFamily}`,
            cell: `${cellStyle.fontWeight} ${cellStyle.fontSize} ${cellStyle.fontFamily}`
        };

        // Clean up
        this.container.removeChild(headerElement);
        this.container.removeChild(cellElement);

        return this._cachedFonts;
    }

    /**
     * Invalidate measurement cache when fonts or sizes change
     */
    invalidateMeasurementCache() {
        this.measurementCache.clear();
        this._cachedFonts = null;
    }

    /**
     * Get column width by index
     */
    getColumnWidth(colIndex) {
        const column = this.config.columns[colIndex];
        if (!column) return 100; // Default width

        // Check if we have a stored width
        if (this.state.columnWidths.has(column.field)) {
            return this.state.columnWidths.get(column.field);
        }

        // Return configured width or default
        return column.width || 100;
    }

    /**
     * Measure text width with per-column measurement cache
     */
    measureTextWidth(text, font, columnField = null) {
        // Create cache key for this measurement
        const cacheKey = columnField ? `${columnField}:${text}:${font}` : `${text}:${font}`;

        // Check cache first
        if (this.measurementCache.has(cacheKey)) {
            return this.measurementCache.get(cacheKey);
        }

        // Create canvas context if not exists
        if (!this._measureCtx) {
            const canvas = document.createElement('canvas');
            this._measureCtx = canvas.getContext('2d');
        }

        this._measureCtx.font = font;
        const width = Math.min(600, this._measureCtx.measureText(text).width); // Cap at 600px

        // Cache the result
        this.measurementCache.set(cacheKey, width);

        // Limit cache size to prevent memory issues
        if (this.measurementCache.size > 1000) {
            const firstKey = this.measurementCache.keys().next().value;
            this.measurementCache.delete(firstKey);
        }

        return width;
    }

    /**
     * Format currency value
     */
    formatCurrency(value, format = {}) {
        const { currency = 'USD', locale = 'en-US' } = format;
        if (value == null || isNaN(value)) return '';

        try {
            return new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: currency
            }).format(Number(value));
        } catch (e) {
            return `$${Number(value).toFixed(2)}`;
        }
    }

    /**
     * Format number value
     */
    formatNumber(value, format = {}) {
        const { decimals = 0, locale = 'en-US' } = format;
        if (value == null || isNaN(value)) return '';

        try {
            return new Intl.NumberFormat(locale, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            }).format(Number(value));
        } catch (e) {
            return Number(value).toFixed(decimals);
        }
    }

    /**
     * Format date value
     */
    formatDate(value, format = {}) {
        const { locale = 'en-US', options = {} } = format;
        if (!value) return '';

        try {
            const date = new Date(value);
            if (isNaN(date.getTime())) return String(value);

            return new Intl.DateTimeFormat(locale, {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                ...options
            }).format(date);
        } catch (e) {
            return String(value);
        }
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHTML(str) {
        if (typeof str !== 'string') return str;

        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    }

    /**
     * Simple HTML sanitizer - allows basic formatting tags
     */
    sanitizeHTML(str) {
        if (typeof str !== 'string') return str;

        // Allow only basic formatting tags
        const allowedTags = ['b', 'i', 'em', 'strong', 'span', 'br'];
        const allowedAttributes = ['class', 'style'];

        // Create a temporary element to parse HTML
        const temp = document.createElement('div');
        temp.innerHTML = str;

        // Recursively clean elements
        const cleanElement = (element) => {
            const tagName = element.tagName?.toLowerCase();

            // Remove disallowed tags
            if (tagName && !allowedTags.includes(tagName)) {
                // Replace with text content
                const textNode = document.createTextNode(element.textContent || '');
                element.parentNode?.replaceChild(textNode, element);
                return;
            }

            // Clean attributes
            if (element.attributes) {
                const attrs = Array.from(element.attributes);
                attrs.forEach(attr => {
                    if (!allowedAttributes.includes(attr.name.toLowerCase())) {
                        element.removeAttribute(attr.name);
                    }
                });
            }

            // Recursively clean children
            Array.from(element.children).forEach(cleanElement);
        };

        Array.from(temp.children).forEach(cleanElement);
        return temp.innerHTML;
    }

    /**
     * Show column chooser dialog
     */
    showColumnChooser() {
        // Remove existing dialog
        this.hideColumnChooser();

        const dialog = document.createElement('div');
        dialog.className = 'snap-grid-column-chooser';
        dialog.setAttribute('role', 'dialog');
        dialog.setAttribute('aria-label', 'Choose Columns');

        dialog.innerHTML = `
            <div class="column-chooser-header">
                <h3>Choose Columns</h3>
                <button class="column-chooser-close" aria-label="Close column chooser">×</button>
            </div>
            <div class="column-chooser-body">
                <div class="column-list">
                    ${this.config.columns.map(column => `
                        <label class="column-item">
                            <input type="checkbox"
                                   value="${column.field}"
                                   ${!this.state.hiddenColumns.has(column.field) ? 'checked' : ''}>
                            <span>${column.headerName || column.field}</span>
                        </label>
                    `).join('')}
                </div>
                <div class="column-chooser-actions">
                    <button class="btn btn-secondary" id="select-all">Select All</button>
                    <button class="btn btn-secondary" id="select-none">Select None</button>
                    <button class="btn btn-primary" id="apply-columns">Apply</button>
                </div>
            </div>
        `;

        // Position dialog
        dialog.style.position = 'fixed';
        dialog.style.top = '50%';
        dialog.style.left = '50%';
        dialog.style.transform = 'translate(-50%, -50%)';
        dialog.style.zIndex = '1001';

        // Position dialog in center of viewport
        this.positionDialog(dialog);

        document.body.appendChild(dialog);
        this.currentColumnChooser = dialog;
        this.activeDialogs.add(dialog);

        // Event listeners
        this.setupColumnChooserEvents(dialog);
    }

    /**
     * Setup column chooser events
     */
    setupColumnChooserEvents(dialog) {
        const closeBtn = dialog.querySelector('.column-chooser-close');
        const selectAllBtn = dialog.querySelector('#select-all');
        const selectNoneBtn = dialog.querySelector('#select-none');
        const applyBtn = dialog.querySelector('#apply-columns');
        const checkboxes = dialog.querySelectorAll('input[type="checkbox"]');

        closeBtn.addEventListener('click', () => this.hideColumnChooser());

        selectAllBtn.addEventListener('click', () => {
            checkboxes.forEach(cb => cb.checked = true);
        });

        selectNoneBtn.addEventListener('click', () => {
            checkboxes.forEach(cb => cb.checked = false);
        });

        applyBtn.addEventListener('click', () => {
            // Update column visibility
            this.config.columns.forEach(column => {
                const checkbox = dialog.querySelector(`input[value="${column.field}"]`);
                if (checkbox.checked) {
                    this.showColumn(column.field);
                } else {
                    this.hideColumn(column.field);
                }
            });

            this.hideColumnChooser();
        });

        // Close on outside click
        setTimeout(() => {
            document.addEventListener('click', (e) => {
                if (!dialog.contains(e.target)) {
                    this.hideColumnChooser();
                }
            }, { once: true });
        }, 100);
    }

    /**
     * Hide column chooser
     */
    hideColumnChooser() {
        if (this.currentColumnChooser) {
            this.activeDialogs.delete(this.currentColumnChooser);
            this.currentColumnChooser.remove();
            this.currentColumnChooser = null;
        }
    }

    /**
     * Reset columns to default state
     */
    resetColumns() {
        // Columns that should not be affected by reset
        const excludedColumns = ['checkbox', 'preview', 'actions'];

        // Reset column widths to their minimum width (excluding special columns)
        this.config.columns.forEach(column => {
            if (!excludedColumns.includes(column.field)) {
                const minWidth = this.calculateMinHeaderWidth(column);
                this.state.columnWidths.set(column.field, minWidth);
            }
        });

        // Reset column visibility (excluding special columns)
        const hiddenColumnsToKeep = new Set();
        this.state.hiddenColumns.forEach(field => {
            if (excludedColumns.includes(field)) {
                hiddenColumnsToKeep.add(field);
            }
        });
        this.state.hiddenColumns = hiddenColumnsToKeep;

        // Reset column pinning to empty state first
        this.state.pinnedColumns = { left: [], right: [] };

        // Reset column order (excluding special columns - they maintain their positions)
        const regularColumns = this.config.columns
            .filter(col => !excludedColumns.includes(col.field))
            .map(col => col.field);

        // Rebuild order with special columns in their default positions
        this.state.columnOrder = [];

        // Add left-pinned special columns first
        if (this.config.columns.find(col => col.field === 'checkbox')) {
            this.state.columnOrder.push('checkbox');
        }
        if (this.config.columns.find(col => col.field === 'preview')) {
            this.state.columnOrder.push('preview');
        }

        // Add regular columns
        this.state.columnOrder.push(...regularColumns);

        // Add right-pinned special columns last
        if (this.config.columns.find(col => col.field === 'actions')) {
            this.state.columnOrder.push('actions');
        }

        // Re-establish default pinning (checkbox, preview to left; actions to right)
        this.setupDefaultPinning();

        this.render();
        this.emit('columnsReset');
    }

    /**
     * Apply pinning styles to cell or header
     */
    applyPinningStyles(element, field) {
        const isLeftPinned = this.state.pinnedColumns.left.includes(field);
        const isRightPinned = this.state.pinnedColumns.right.includes(field);

        // Remove all pinning classes first
        element.classList.remove('pinned-left', 'pinned-right');

        if (isLeftPinned) {
            element.classList.add('pinned-left');
            element.style.position = 'sticky';
            element.style.left = this.calculateLeftPinOffset(field) + 'px';
            element.style.zIndex = '3';
            element.style.backgroundColor = 'var(--grid-header-bg)';
        } else if (isRightPinned) {
            element.classList.add('pinned-right');
            element.style.position = 'sticky';
            element.style.right = this.calculateRightPinOffset(field) + 'px';
            element.style.zIndex = '3';
            element.style.backgroundColor = 'var(--grid-header-bg)';
        } else {
            // Remove pinning styles
            element.style.position = '';
            element.style.left = '';
            element.style.right = '';
            element.style.zIndex = '';
            element.style.backgroundColor = '';
        }
    }

    /**
     * Calculate left pin offset for a field
     */
    calculateLeftPinOffset(field) {
        let offset = 0;
        const leftPinnedIndex = this.state.pinnedColumns.left.indexOf(field);

        for (let i = 0; i < leftPinnedIndex; i++) {
            const pinnedField = this.state.pinnedColumns.left[i];
            offset += this.state.columnWidths.get(pinnedField) || 150;
        }

        return offset;
    }

    /**
     * Calculate right pin offset for a field
     */
    calculateRightPinOffset(field) {
        let offset = 0;
        const rightPinnedIndex = this.state.pinnedColumns.right.indexOf(field);

        for (let i = rightPinnedIndex + 1; i < this.state.pinnedColumns.right.length; i++) {
            const pinnedField = this.state.pinnedColumns.right[i];
            offset += this.state.columnWidths.get(pinnedField) || 150;
        }

        return offset;
    }

    /**
     * Calculate total width of pinned columns on specified side
     */
    calculateTotalPinnedWidth(side) {
        const pinnedColumns = this.state.pinnedColumns[side] || [];
        let totalWidth = 0;

        pinnedColumns.forEach(field => {
            const width = this.state.columnWidths.get(field) || 0;
            totalWidth += width;
        });

        return totalWidth;
    }

    /**
     * Position dialog in viewport center with edge detection
     */
    positionDialog(dialog) {
        dialog.style.position = 'fixed';
        dialog.style.top = '50%';
        dialog.style.left = '50%';
        dialog.style.transform = 'translate(-50%, -50%)';
        dialog.style.zIndex = '1001';
        dialog.style.maxHeight = '90vh';
        dialog.style.maxWidth = '90vw';
    }

    /**
     * Position menu with collision detection during scrolling
     */
    positionMenuWithCollisionDetection(menu, targetElement) {
        // Ensure menu has proper positioning styles
        menu.style.position = 'absolute';
        menu.style.zIndex = '1001';
        
        const rect = targetElement.getBoundingClientRect();
        const containerRect = this.container.getBoundingClientRect();
        const menuRect = menu.getBoundingClientRect();
        const menuWidth = menuRect.width || 281; // Fallback to CSS width if not rendered yet
        const menuHeight = menuRect.height || 400; // Fallback to CSS height if not rendered yet
        const padding = 8;

        // Get pinned column boundaries in viewport coordinates
        const pinnedLeftWidth = this.calculateTotalPinnedWidth('left');
        const pinnedRightWidth = this.calculateTotalPinnedWidth('right');
        const containerWidth = this.container.offsetWidth;

        // Calculate pinned boundaries in viewport coordinates
        const leftPinnedEnd = containerRect.left + pinnedLeftWidth;
        const rightPinnedStart = containerRect.right - pinnedRightWidth;

        // Calculate scroll-adjusted positions
        const scrollLeft = this.container.scrollLeft || 0;
        const scrollTop = this.container.scrollTop || 0;

        const targetRelativeToContainer = {
            left: rect.left - containerRect.left + scrollLeft,
            right: rect.right - containerRect.left + scrollLeft,
            top: rect.top - containerRect.top + scrollTop,
            bottom: rect.bottom - containerRect.top + scrollTop,
            width: rect.width
        };

        // Determine original menu position preference
        const containerCenter = containerWidth / 2;
        const columnCenter = targetRelativeToContainer.left + (targetRelativeToContainer.width / 2);
        const isLeftSide = columnCenter < containerCenter;

        let left;

        if (isLeftSide) {
            // Prefer right side of column
            left = targetRelativeToContainer.right;

            // Check collision with right pinned columns using viewport coordinates
            const menuRightEdge = containerRect.left + left + menuWidth - scrollLeft;
            if (menuRightEdge > rightPinnedStart && pinnedRightWidth > 0) {
                // Stop at right pinned boundary (convert back to container coordinates)
                left = rightPinnedStart - containerRect.left + scrollLeft - menuWidth;
            }
        } else {
            // Prefer left side of column (account for 1.5px border)
            left = targetRelativeToContainer.left - menuWidth + 1.5;

            // Check collision with left pinned columns using viewport coordinates
            const menuLeftEdge = containerRect.left + left - scrollLeft;
            if (menuLeftEdge < leftPinnedEnd && pinnedLeftWidth > 0) {
                // Stop at left pinned boundary (convert back to container coordinates)
                left = leftPinnedEnd - containerRect.left + scrollLeft;
            }
        }

        // Ensure menu stays within container bounds
        if (left < 0) left = 0;
        if (left + menuWidth > containerWidth) left = containerWidth - menuWidth;

        // Vertical positioning
        let top = targetRelativeToContainer.bottom + 2;

        if (top + menuHeight > this.container.offsetHeight - padding) {
            top = targetRelativeToContainer.top - menuHeight - 2;
            if (top < padding) {
                top = padding;
            }
        }

        // Apply positioning
        menu.style.left = `${left}px`;
        menu.style.top = `${top}px`;
    }

    /**
     * Position menu relative to target element with smart left/right positioning
     */
    positionMenu(menu, targetElement) {
        const rect = targetElement.getBoundingClientRect();
        const containerRect = this.container.getBoundingClientRect();
        const padding = 8; // Container padding

        // Position menu absolutely within the grid container
        menu.style.position = 'absolute';
        menu.style.zIndex = '1001';

        // Force menu to be visible to get accurate dimensions
        menu.style.visibility = 'hidden';
        menu.style.display = 'block';

        // Get actual menu dimensions
        const menuRect = menu.getBoundingClientRect();
        const menuWidth = menuRect.width || 300; // Fallback to 300 if not rendered yet
        const menuHeight = menuRect.height || 400; // Fallback to 400 if not rendered yet

        // Calculate positions relative to container's scroll position
        const scrollLeft = this.container.scrollLeft || 0;
        const scrollTop = this.container.scrollTop || 0;

        const targetRelativeToContainer = {
            left: Math.round(rect.left - containerRect.left + scrollLeft),
            right: Math.round(rect.right - containerRect.left + scrollLeft),
            top: Math.round(rect.top - containerRect.top + scrollTop),
            bottom: Math.round(rect.bottom - containerRect.top + scrollTop),
            width: rect.width
        };

        // Determine if column is on left or right side of container
        const containerCenter = this.container.offsetWidth / 2;
        const columnCenter = targetRelativeToContainer.left + (targetRelativeToContainer.width / 2);
        const isLeftSide = columnCenter < containerCenter;

        // Smart horizontal positioning with collision detection for pinned columns
        let left;

        // Get pinned column boundaries
        const pinnedLeftWidth = this.calculateTotalPinnedWidth('left');
        const pinnedRightWidth = this.calculateTotalPinnedWidth('right');
        const containerWidth = this.container.offsetWidth;

        if (isLeftSide) {
            // Column is on left side → open menu to the right
            left = targetRelativeToContainer.right; // No gap - exactly at column edge

            // Check collision with right pinned columns
            const rightPinnedStart = containerWidth - pinnedRightWidth;
            if (left + menuWidth > rightPinnedStart && pinnedRightWidth > 0) {
                // Menu would collide with right pinned columns, position it to the left instead
                left = rightPinnedStart - menuWidth;
            }

            // Ensure menu doesn't go off right edge of container
            if (left + menuWidth > containerWidth - padding) {
                left = containerWidth - menuWidth - padding;
            }
        } else {
            // Column is on right side → open menu to the left
            // Position menu's right edge exactly at column's left edge (account for 1.5px border)
            left = targetRelativeToContainer.left - menuWidth + 1.5; // +1.5 to account for border

            // Check collision with left pinned columns
            if (left < pinnedLeftWidth && pinnedLeftWidth > 0) {
                // Menu would collide with left pinned columns, position it to the right instead
                left = pinnedLeftWidth;
            }

            // Ensure menu doesn't go off left edge of container
            if (left < padding) {
                left = padding;
            }
        }

        // Vertical positioning relative to container
        let top = targetRelativeToContainer.bottom + 2; // Small gap below the column header

        // Ensure menu doesn't go off bottom edge of container
        if (top + menuHeight > this.container.offsetHeight - padding) {
            top = targetRelativeToContainer.top - menuHeight - 2; // Position above if no space below
            if (top < padding) {
                top = padding; // Fallback to top of container
            }
        }

        // Apply positioning with pixel-perfect rounding
        menu.style.left = `${Math.round(left)}px`;
        menu.style.top = `${Math.round(top)}px`;

        // Make menu visible again
        menu.style.visibility = 'visible';

        // Set position attribute for debugging
        const position = isLeftSide ? 'left-column-right-menu' : 'right-column-left-menu';
        menu.setAttribute('data-position', position);
    }

    /**
     * Toggle sort for a field
     */
    toggleSort(field, multiSort = false) {
        const existingSort = this.state.sortConfig.find(s => s.field === field);

        if (!multiSort) {
            // Single column sort
            if (existingSort) {
                if (existingSort.direction === 'asc') {
                    this.state.sortConfig = [{ field, direction: 'desc' }];
                } else {
                    this.state.sortConfig = [];
                }
            } else {
                this.state.sortConfig = [{ field, direction: 'asc' }];
            }
        } else {
            // Multi-column sort
            if (existingSort) {
                if (existingSort.direction === 'asc') {
                    existingSort.direction = 'desc';
                } else {
                    this.state.sortConfig = this.state.sortConfig.filter(s => s.field !== field);
                }
            } else {
                this.state.sortConfig.push({ field, direction: 'asc' });
            }
        }

        this.processData();
        this.render();
        this.emit('sortChanged', { sortConfig: this.state.sortConfig });
    }

    /**
     * Set sort configuration
     */
    setSortConfig(sortConfig) {
        this.state.sortConfig = sortConfig;
        this.processData();
        this.render();
        this.emit('sortChanged', { sortConfig: this.state.sortConfig });
    }

    /**
     * Set filter for a field
     */
    setFilter(field, filter) {
        if (filter) {
            this.state.filterConfig[field] = filter;
        } else {
            delete this.state.filterConfig[field];
        }

        this.processData();
        this.render();
        this.updateFilterIndicators(); // Ensure filter indicators are updated
        this.emit('filterChanged', { filterConfig: this.state.filterConfig });
    }

    /**
     * Clear filter for a field
     */
    clearFilter(field) {
        delete this.state.filterConfig[field];
        this.processData();
        this.render();
        this.updateFilterIndicators(); // Ensure filter indicators are updated
        this.emit('filterChanged', { filterConfig: this.state.filterConfig });
    }

    /**
     * Update filter indicators for all columns
     */
    updateFilterIndicators() {
        // Use setTimeout to ensure DOM is fully rendered
        setTimeout(() => {
            // Find all header cells and update their filter indicators
            const headerCells = this.container.querySelectorAll('.snap-grid-header-cell');

            console.log('Updating filter indicators for', headerCells.length, 'header cells');
            console.log('Current filterConfig:', this.state.filterConfig);

            headerCells.forEach(cell => {
                const field = cell.getAttribute('data-field');
                if (!field) return;

                const menuContainer = cell.querySelector('.snap-grid-column-menu-container');
                if (!menuContainer) {
                    console.log('No menu container found for field:', field);
                    return;
                }

                // Remove existing indicator
                const existingIndicator = menuContainer.querySelector('.snap-grid-filter-indicator');
                if (existingIndicator) {
                    existingIndicator.remove();
                }

                // Add new indicator if filter exists
                if (this.state.filterConfig[field]) {
                    console.log('Adding filter indicator for field:', field);
                    const indicator = document.createElement('div');
                    indicator.className = 'snap-grid-filter-indicator';
                    indicator.style.position = 'absolute';
                    indicator.style.top = '2px';
                    indicator.style.right = '2px';
                    indicator.style.width = '6px';
                    indicator.style.height = '6px';
                    indicator.style.borderRadius = '50%';
                    indicator.style.background = 'var(--color-primary-600, #470CED)';
                    indicator.style.pointerEvents = 'none';
                    indicator.style.zIndex = '1';
                    indicator.setAttribute('title', 'Filter applied');

                    menuContainer.appendChild(indicator);
                } else {
                    console.log('No filter for field:', field);
                }
            });
        }, 0);
    }

    /**
     * Set group configuration
     */
    setGroupConfig(groupConfig) {
        this.state.groupConfig = groupConfig;

        // Initialize all groups as expanded by default
        if (groupConfig) {
            this.state.expandedGroups.clear();
            // Get unique group values and expand all
            const uniqueGroups = new Set();
            this.state.sortedData.forEach(row => {
                const groupValue = this.getNestedValue(row, groupConfig.field);
                const key = String(groupValue || '');
                uniqueGroups.add(key);
            });
            uniqueGroups.forEach(key => this.state.expandedGroups.add(key));
        } else {
            this.state.expandedGroups.clear();
        }

        this.processData();
        this.render();
        this.emit('groupChanged', { groupConfig: this.state.groupConfig });
    }

    /**
     * Set column width
     */
    setColumnWidth(field, width) {
        // Enforce minimum width based on header text
        const column = this.config.columns.find(col => col.field === field);
        const minWidth = column ? this.calculateMinHeaderWidth(column) : 80;
        const finalWidth = Math.max(minWidth, width);

        this.state.columnWidths.set(field, finalWidth);

        // Directly update all cells for this column to ensure synchronization
        this.updateColumnWidthDOM(field, finalWidth);

        this.emit('columnResized', { field, width: finalWidth });
    }

    /**
     * Update column width in DOM directly for efficiency and synchronization
     */
    updateColumnWidthDOM(field, width) {
        const widthPx = `${width}px`;

        // Update header cell
        const headerCell = this.container.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
        if (headerCell) {
            headerCell.style.width = widthPx;
        }

        // Update all data cells for this column
        const dataCells = this.container.querySelectorAll(`.snap-grid-cell[data-field="${field}"]`);
        dataCells.forEach(cell => {
            cell.style.width = widthPx;
        });

        // If column is pinned, we need to recalculate pin offsets and trigger a render
        if (this.isColumnPinned(field)) {
            this.render();
        }
    }

    /**
     * Hide column
     */
    hideColumn(field) {
        this.state.hiddenColumns.add(field);
        this.render();
        this.emit('columnHidden', { field });
    }

    /**
     * Show column
     */
    showColumn(field) {
        this.state.hiddenColumns.delete(field);
        this.render();
        this.emit('columnShown', { field });
    }

    /**
     * Select row by index
     * @param {number} rowIndex - Index of the row to select
     * @param {boolean} multiSelect - Whether to allow multiple selections
     * @emits selectionChanged - { selectedRowKeys: string[], selectedData: any[] }
     */
    selectRow(rowIndex, multiSelect = false) {
        const rowData = this.state.displayData[rowIndex];
        if (!rowData || rowData.__isGroupHeader) return;

        const rowKey = this.getRowId(rowData);

        if (!multiSelect) {
            this.state.selectedRowKeys.clear();
        }

        if (this.state.selectedRowKeys.has(rowKey)) {
            this.state.selectedRowKeys.delete(rowKey);
        } else {
            this.state.selectedRowKeys.add(rowKey);
        }

        this.render();
        this.emitSelectionChanged();
    }



    /**
     * Select all visible rows
     * @emits selectionChanged - { selectedRowKeys: string[], selectedData: any[] }
     */
    selectAll() {
        this.state.selectedRowKeys.clear();
        this.state.displayData.forEach(rowData => {
            if (!rowData.__isGroupHeader) {
                const rowKey = this.getRowId(rowData);
                this.state.selectedRowKeys.add(rowKey);
            }
        });

        this.render();
        this.emitSelectionChanged();
    }

    /**
     * Clear all selections
     * @emits selectionChanged - { selectedRowKeys: string[], selectedData: any[] }
     */
    clearSelection() {
        this.state.selectedRowKeys.clear();
        this.render();
        this.emitSelectionChanged();
    }

    /**
     * Get selected data using stable keys
     * @returns {any[]} Array of selected row data objects
     */
    getSelectedData() {
        const selectedData = [];
        this.state.selectedRowKeys.forEach(key => {
            // Find row in original data by key
            const rowData = this.state.data.find(row => this.getRowId(row) === key);
            if (rowData) {
                selectedData.push(rowData);
            }
        });
        return selectedData;
    }

    /**
     * Get count of selected rows
     * @returns {number} Number of selected rows
     */
    getSelectionCount() {
        return this.state.selectedRowKeys.size;
    }

    /**
     * Emit standardized selection changed event
     * @private
     */
    emitSelectionChanged() {
        this.emit('selectionChanged', {
            selectedRowKeys: Array.from(this.state.selectedRowKeys),
            selectedData: this.getSelectedData()
        });
    }





    /**
     * Update data
     */
    updateData(newData) {
        this.config.data = newData;
        this.processData();
        this.render();
        this.emit('dataUpdated', { data: newData });
    }

    /**
     * Public resize method for external use
     */
    resize() {
        this.handleResize();
    }

    /**
     * Export data to CSV
     */
    exportCSV(options = {}) {
        const {
            selectedOnly = false,
            visibleOnly = true,
            filename = 'grid-export.csv'
        } = options;

        // Determine which columns to include
        let columns = visibleOnly
            ? this.config.columns.filter(col => !this.state.hiddenColumns.has(col.field))
            : this.config.columns;

        // Sort columns by order if defined
        if (this.state.columnOrder && this.state.columnOrder.length > 0) {
            columns = columns.sort((a, b) => {
                const aIndex = this.state.columnOrder.indexOf(a.field);
                const bIndex = this.state.columnOrder.indexOf(b.field);
                return aIndex - bIndex;
            });
        }

        // Determine which rows to include - always exclude group headers
        let rows;
        if (selectedOnly) {
            rows = this.getSelectedData().filter(row => !row.__isGroupHeader);
        } else {
            rows = visibleOnly
                ? this.state.displayData.filter(row => !row.__isGroupHeader)
                : this.state.data.filter(row => !row.__isGroupHeader);
        }

        // Build CSV content
        const csvContent = this.buildCSVContent(columns, rows);

        // Create and trigger download
        this.downloadCSV(csvContent, filename);

        this.emit('csvExported', {
            filename,
            rowCount: rows.length,
            columnCount: columns.length
        });
    }

    /**
     * Build CSV content from columns and rows
     */
    buildCSVContent(columns, rows) {
        const lines = [];

        // Header row
        const headers = columns.map(col => this.escapeCSVValue(col.headerName || col.field));
        lines.push(headers.join(','));

        // Data rows
        rows.forEach(row => {
            const values = columns.map(col => {
                const value = this.getCellValue(row, col);
                let displayValue = value;

                // Format values for CSV
                if (col.type === 'currency') {
                    displayValue = this.formatCurrency(value, col.currencyFormat);
                } else if (col.type === 'number') {
                    displayValue = this.formatNumber(value, col.numberFormat);
                } else if (col.type === 'date') {
                    displayValue = this.formatDate(value, col.dateFormat);
                } else if (col.type === 'boolean') {
                    displayValue = Boolean(value) ? 'Yes' : 'No';
                }

                return this.escapeCSVValue(displayValue);
            });
            lines.push(values.join(','));
        });

        return lines.join('\n');
    }

    /**
     * Escape CSV value
     */
    escapeCSVValue(value) {
        if (value == null) return '';

        const str = String(value);

        // If value contains comma, newline, or quote, wrap in quotes and escape quotes
        if (str.includes(',') || str.includes('\n') || str.includes('"')) {
            return '"' + str.replace(/"/g, '""') + '"';
        }

        return str;
    }

    /**
     * Download CSV content as file
     */
    downloadCSV(content, filename) {
        const blob = new Blob(['\uFEFF' + content], {
            type: 'text/csv;charset=utf-8;'
        }); // \uFEFF is BOM for UTF-8

        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up object URL
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    /**
     * Add event listener
     */
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    /**
     * Remove event listener
     */
    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Emit event
     */
    emit(event, data = {}) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        this.elements.overlay.innerHTML = `
            <div class="snap-grid-error">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${message}</div>
                <button class="error-close" onclick="this.parentElement.parentElement.style.display='none'">×</button>
            </div>
        `;
        this.elements.overlay.style.display = 'flex';
    }

    /**
     * Render footer
     */
    renderFooter() {
        if (!this.config.pagination) {
            this.elements.footer.style.display = 'none';
            return;
        }

        // Get total before pagination
        const totalRows = this.state.sortedData.length;
        const pageSize = this.config.pageSize;
        const totalPages = Math.ceil(totalRows / pageSize);
        const currentPage = this.state.pageIndex + 1;
        const startRow = this.state.pageIndex * pageSize + 1;
        const endRow = Math.min(startRow + pageSize - 1, totalRows);

        this.elements.footer.innerHTML = `
            <div class="snap-grid-pagination">
                <span class="pagination-info">
                    Showing ${startRow}-${endRow} of ${totalRows} rows
                </span>
                <div class="pagination-controls">
                    <button class="pagination-btn" data-action="first" ${this.state.pageIndex === 0 ? 'disabled' : ''}>First</button>
                    <button class="pagination-btn" data-action="prev" ${this.state.pageIndex === 0 ? 'disabled' : ''}>Previous</button>
                    <span class="pagination-pages">Page ${currentPage} of ${totalPages}</span>
                    <button class="pagination-btn" data-action="next" ${this.state.pageIndex >= totalPages - 1 ? 'disabled' : ''}>Next</button>
                    <button class="pagination-btn" data-action="last" ${this.state.pageIndex >= totalPages - 1 ? 'disabled' : ''}>Last</button>
                </div>
            </div>
        `;

        // Add event listeners for pagination buttons
        this.elements.footer.querySelectorAll('.pagination-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.getAttribute('data-action');
                this.handlePaginationAction(action);
            });
        });
    }

    /**
     * Handle pagination button clicks
     */
    handlePaginationAction(action) {
        const totalRows = this.state.sortedData.length;
        const totalPages = Math.ceil(totalRows / this.config.pageSize);

        switch (action) {
            case 'first':
                this.state.pageIndex = 0;
                break;
            case 'prev':
                this.state.pageIndex = Math.max(0, this.state.pageIndex - 1);
                break;
            case 'next':
                this.state.pageIndex = Math.min(totalPages - 1, this.state.pageIndex + 1);
                break;
            case 'last':
                this.state.pageIndex = totalPages - 1;
                break;
        }

        this.processData();
        this.render();
        this.emit('pageChanged', { pageIndex: this.state.pageIndex });
    }

    /**
     * Destroy grid and cleanup
     */
    destroy() {
        // Remove event listeners
        this.boundHandlers.forEach((handler, event) => {
            if (event === 'scroll' && this.elements?.viewport) {
                this.elements.viewport.removeEventListener('scroll', handler);
            } else if (event === 'pinnedScroll') {
                if (this.elements?.pinnedLeft) {
                    this.elements.pinnedLeft.removeEventListener('scroll', handler);
                }
                if (this.elements?.pinnedRight) {
                    this.elements.pinnedRight.removeEventListener('scroll', handler);
                }
            } else if (event === 'resize') {
                window.removeEventListener('resize', handler);
            } else if (this.container) {
                this.container.removeEventListener(event, handler);
            }
        });

        // Clear all active dialogs and menus
        this.activeDialogs.forEach(dialog => {
            if (dialog.parentNode) {
                dialog.remove();
            }
        });
        this.activeDialogs.clear();

        // Hide specific dialogs/menus with null checks
        try {
            this.hideColumnMenu();
        } catch (e) {
            console.warn('Error hiding column menu:', e);
        }

        try {
            this.hideFilterDialog();
        } catch (e) {
            console.warn('Error hiding filter dialog:', e);
        }

        try {
            this.hideColumnChooser();
        } catch (e) {
            console.warn('Error hiding column chooser:', e);
        }



        // Disconnect performance observer if exists
        if (this.performanceObserver) {
            this.performanceObserver.disconnect();
            this.performanceObserver = null;
        }

        // Clear references with null checks
        if (this.eventHandlers) {
            this.eventHandlers.clear();
        }
        if (this.boundHandlers) {
            this.boundHandlers.clear();
        }

        // Clean up DOM elements
        if (this.elements?.overlay) {
            this.elements.overlay.innerHTML = '';
        }

        this.elements = null;
        this._measureCtx = null;
        this._cachedFonts = null;

        // Remove grid classes but preserve other classes
        if (this.container) {
            this.container.classList.remove('snap-grid', this.config?.theme || 'default');
            this.container.innerHTML = '';
        }

        this.emit('destroyed');
    }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SnapGrid;
} else if (typeof window !== 'undefined') {
    window.SnapGrid = SnapGrid;
}
